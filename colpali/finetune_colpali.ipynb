{"cells": [{"cell_type": "markdown", "metadata": {"id": "i72MPXVuA7Is"}, "source": ["# Fine-tune ColPali 🛠️\n", "\n", "[![Colab](https://img.shields.io/badge/Open_in_Colab-F9AB00?logo=googlecolab&logoColor=fff&style=for-the-badge)](https://colab.research.google.com/github/tonywu71/colpali-cookbooks/blob/main/examples/finetune_colpali.ipynb)\n", "[![GitHub](https://img.shields.io/badge/ColPali_Cookbooks-100000?style=for-the-badge&logo=github&logoColor=white)](https://github.com/tonywu71/colpali-cookbooks)\n", "[![arXiv](https://img.shields.io/badge/arXiv-2407.01449-b31b1b.svg?style=for-the-badge)](https://arxiv.org/abs/2407.01449)\n", "[![Hugging Face](https://img.shields.io/badge/Vidore-FFD21E?style=for-the-badge&logo=huggingface&logoColor=000)](https://huggingface.co/vidore)\n", "[![X](https://img.shields.io/badge/Thread-%23000000?style=for-the-badge&logo=X&logoColor=white)](https://x.com/tonywu_71/status/1839281156811874515)\n", "\n", "## Introduction\n", "\n", "With our new model *ColPali*, we propose to leverage VLMs to construct efficient multi-vector embeddings in the visual space for document retrieval. By feeding the ViT output patches from PaliGemma-3B to a linear projection, we create a multi-vector representation of documents. We train the model to maximize the similarity between these document embeddings and the query embeddings, following the ColBERT method.\n", "\n", "Using ColPali removes the need for potentially complex and brittle layout recognition and OCR pipelines with a single model that can take into account both the textual and visual content (layout, charts, ...) of a document.\n", "\n", "![ColPali Architecture](https://github.com/tonywu71/colpali-cookbooks/blob/main/assets/architecture/colpali_architecture.jpeg?raw=true)\n", "\n", "The following notebook guides you through how to fine-tune ColPali to improve its retrieval performance for the data distribution of your use case. In particular, we will fine-tune ColPali on [VDSID-French](https://huggingface.co/datasets/vidore/vdsid_french), a French-language document retrieval dataset.\n", "\n", "## What if I want to use my own documents to fine-tune ColPali?\n", "\n", "If you are a company, you probably want to fine-tune ColPali on your own documents. But they probably lack the queries that are necessary to train a vision retrieval model... But fear not as <PERSON> has published an awesome [🤗 blog post](https://danielvanstrien.xyz/posts/post-with-code/colpali/2024-09-23-generate_colpali_dataset.html) on how to use VLMs to generate quality queries for your PDFs and to create a dataset that you can use for fine-tuning.\n", "\n", "## Hardware Requirements\n", "\n", "This notebook was tested on GCP VM with an A100-40GB GPU. I recommend seting this VM up with [SkyPilot](https://github.com/skypilot-org/skypilot) using this [config](https://github.com/tonywu71/colpali-cookbooks/blob/main/skypilot/a100/config.yaml). You should also be able to run it on a smaller GPU but you'll need a stronger quantization strategy and a smaller batch size."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# ==========================     USER INPUTS     ==========================\n", "\n", "# Define the name used for the model you will push to the HuggingFace Hub.\n", "# Leave it empty to disable pushing the model.\n", "hf_pushed_model_name = \"tonywu71/finetune_colpali_v1_2-vdsid_french-4bit\"\n", "\n", "# Define the name used for the WandB experiment. Leave it empty to disable WandB logging.\n", "# In particular, leave it empty if you don't have a WandB account.\n", "wandb_experiment_name = \"finetune_colpali_v1_2-vdsid_french-4bit\"\n", "\n", "# =========================================================================\n", "\n", "if not wandb_experiment_name:\n", "    print(\"WandB logging is disabled.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Installation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This notebook leverages [`colpali-engine`](https://github.com/illuin-tech/colpali), the official implementation of ColPali. This package also contains the training code (processor, collator, trainer...) for fine-tuning ColPali on your own dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {"metadata": {}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m25.1.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.2\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["# !pip install -q -U \"colpali-engine[train]>=0.3.0,<0.4.0\""]}, {"cell_type": "code", "execution_count": 5, "metadata": {"metadata": {}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: mteb in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (1.38.42)\n", "Requirement already satisfied: datasets<4.0.0,>=2.19.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from mteb) (3.1.0)\n", "Requirement already satisfied: numpy<3.0.0,>=1.0.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from mteb) (1.26.4)\n", "Requirement already satisfied: requests>=2.26.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from mteb) (2.32.3)\n", "Requirement already satisfied: scikit_learn>=1.0.2 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from mteb) (1.5.2)\n", "Requirement already satisfied: scipy>=0.0.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from mteb) (1.13.1)\n", "Requirement already satisfied: sentence_transformers>=3.0.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from mteb) (3.2.1)\n", "Requirement already satisfied: typing-extensions>=4.5.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from mteb) (4.12.2)\n", "Requirement already satisfied: torch>1.0.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from mteb) (2.7.1)\n", "Requirement already satisfied: tqdm>1.0.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from mteb) (4.66.4)\n", "Requirement already satisfied: rich>=0.0.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from mteb) (13.9.4)\n", "Requirement already satisfied: pytrec-eval-terrier>=0.5.6 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from mteb) (0.5.6)\n", "Requirement already satisfied: pydantic>=2.0.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from mteb) (2.10.6)\n", "Requirement already satisfied: eval_type_backport>=0.0.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from mteb) (0.2.0)\n", "Requirement already satisfied: polars>=0.20.22 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from mteb) (1.12.0)\n", "Requirement already satisfied: filelock in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from datasets<4.0.0,>=2.19.0->mteb) (3.17.0)\n", "Requirement already satisfied: pyarrow>=15.0.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from datasets<4.0.0,>=2.19.0->mteb) (18.0.0)\n", "Requirement already satisfied: dill<0.3.9,>=0.3.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from datasets<4.0.0,>=2.19.0->mteb) (0.3.8)\n", "Requirement already satisfied: pandas in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from datasets<4.0.0,>=2.19.0->mteb) (2.2.3)\n", "Requirement already satisfied: xxhash in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from datasets<4.0.0,>=2.19.0->mteb) (3.5.0)\n", "Requirement already satisfied: multiprocess<0.70.17 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from datasets<4.0.0,>=2.19.0->mteb) (0.70.16)\n", "Requirement already satisfied: fsspec<=2024.9.0,>=2023.1.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from fsspec[http]<=2024.9.0,>=2023.1.0->datasets<4.0.0,>=2.19.0->mteb) (2024.3.1)\n", "Requirement already satisfied: aiohttp in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from datasets<4.0.0,>=2.19.0->mteb) (3.10.10)\n", "Requirement already satisfied: huggingface-hub>=0.23.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from datasets<4.0.0,>=2.19.0->mteb) (0.34.4)\n", "Requirement already satisfied: packaging in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from datasets<4.0.0,>=2.19.0->mteb) (24.0)\n", "Requirement already satisfied: pyyaml>=5.1 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from datasets<4.0.0,>=2.19.0->mteb) (6.0.1)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from aiohttp->datasets<4.0.0,>=2.19.0->mteb) (2.4.3)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from aiohttp->datasets<4.0.0,>=2.19.0->mteb) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from aiohttp->datasets<4.0.0,>=2.19.0->mteb) (24.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from aiohttp->datasets<4.0.0,>=2.19.0->mteb) (1.5.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from aiohttp->datasets<4.0.0,>=2.19.0->mteb) (6.1.0)\n", "Requirement already satisfied: yarl<2.0,>=1.12.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from aiohttp->datasets<4.0.0,>=2.19.0->mteb) (1.17.1)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from aiohttp->datasets<4.0.0,>=2.19.0->mteb) (4.0.3)\n", "Requirement already satisfied: idna>=2.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from yarl<2.0,>=1.12.0->aiohttp->datasets<4.0.0,>=2.19.0->mteb) (3.7)\n", "Requirement already satisfied: propcache>=0.2.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from yarl<2.0,>=1.12.0->aiohttp->datasets<4.0.0,>=2.19.0->mteb) (0.2.0)\n", "Requirement already satisfied: hf-xet<2.0.0,>=1.1.3 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from huggingface-hub>=0.23.0->datasets<4.0.0,>=2.19.0->mteb) (1.1.3)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from pydantic>=2.0.0->mteb) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from pydantic>=2.0.0->mteb) (2.27.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from requests>=2.26.0->mteb) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from requests>=2.26.0->mteb) (2.2.1)\n", "Requirement already satisfied: certifi>=2017.4.17 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from requests>=2.26.0->mteb) (2024.2.2)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from rich>=0.0.0->mteb) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from rich>=0.0.0->mteb) (2.18.0)\n", "Requirement already satisfied: mdurl~=0.1 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from markdown-it-py>=2.2.0->rich>=0.0.0->mteb) (0.1.2)\n", "Requirement already satisfied: joblib>=1.2.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from scikit_learn>=1.0.2->mteb) (1.4.2)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from scikit_learn>=1.0.2->mteb) (3.5.0)\n", "Requirement already satisfied: transformers<5.0.0,>=4.41.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from sentence_transformers>=3.0.0->mteb) (4.53.3)\n", "Requirement already satisfied: Pillow in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from sentence_transformers>=3.0.0->mteb) (10.4.0)\n", "Requirement already satisfied: regex!=2019.12.17 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from transformers<5.0.0,>=4.41.0->sentence_transformers>=3.0.0->mteb) (2024.4.28)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from transformers<5.0.0,>=4.41.0->sentence_transformers>=3.0.0->mteb) (0.21.4)\n", "Requirement already satisfied: safetensors>=0.4.3 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from transformers<5.0.0,>=4.41.0->sentence_transformers>=3.0.0->mteb) (0.4.3)\n", "Requirement already satisfied: sympy>=1.13.3 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch>1.0.0->mteb) (1.14.0)\n", "Requirement already satisfied: networkx in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch>1.0.0->mteb) (3.2.1)\n", "Requirement already satisfied: jinja2 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch>1.0.0->mteb) (3.1.5)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.6.77 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch>1.0.0->mteb) (12.6.77)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.6.77 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch>1.0.0->mteb) (12.6.77)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.6.80 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch>1.0.0->mteb) (12.6.80)\n", "Requirement already satisfied: nvidia-cudnn-cu12==9.5.1.17 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch>1.0.0->mteb) (9.5.1.17)\n", "Requirement already satisfied: nvidia-cublas-cu12==12.6.4.1 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch>1.0.0->mteb) (12.6.4.1)\n", "Requirement already satisfied: nvidia-cufft-cu12==11.3.0.4 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch>1.0.0->mteb) (11.3.0.4)\n", "Requirement already satisfied: nvidia-curand-cu12==10.3.7.77 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch>1.0.0->mteb) (10.3.7.77)\n", "Requirement already satisfied: nvidia-cusolver-cu12==11.7.1.2 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch>1.0.0->mteb) (11.7.1.2)\n", "Requirement already satisfied: nvidia-cusparse-cu12==12.5.4.2 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch>1.0.0->mteb) (12.5.4.2)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.3 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch>1.0.0->mteb) (0.6.3)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.26.2 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch>1.0.0->mteb) (2.26.2)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.6.77 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch>1.0.0->mteb) (12.6.77)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12==12.6.85 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch>1.0.0->mteb) (12.6.85)\n", "Requirement already satisfied: nvidia-cufile-cu12==1.11.1.6 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch>1.0.0->mteb) (1.11.1.6)\n", "Requirement already satisfied: triton==3.3.1 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch>1.0.0->mteb) (3.3.1)\n", "Requirement already satisfied: setuptools>=40.8.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from triton==3.3.1->torch>1.0.0->mteb) (68.2.2)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from sympy>=1.13.3->torch>1.0.0->mteb) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from jinja2->torch>1.0.0->mteb) (2.1.5)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from pandas->datasets<4.0.0,>=2.19.0->mteb) (2.9.0)\n", "Requirement already satisfied: pytz>=2020.1 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from pandas->datasets<4.0.0,>=2.19.0->mteb) (2024.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from pandas->datasets<4.0.0,>=2.19.0->mteb) (2024.2)\n", "Requirement already satisfied: six>=1.5 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from python-dateutil>=2.8.2->pandas->datasets<4.0.0,>=2.19.0->mteb) (1.16.0)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m25.1.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.2\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["# !pip install mteb"]}, {"cell_type": "markdown", "metadata": {"id": "VWFA1qPc9Z4J"}, "source": ["## Login to a HuggingFace account"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Because ColPali uses the [PaliGemma3B](https://huggingface.co/google/paligemma-3b-mix-448) checkpoints, you need to accept its terms and conditions before using it. Once accepted, use the following cell to login to your HuggingFace account."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 145, "referenced_widgets": ["dd7b36d536434e019a3d84b951c96831", "2b8d3565706c4413af5d0c0a30ad132c", "b2d9f77ebfb144de9e978e309d640041", "5911b53437694805b28573fbeb1594d0", "e45351ae2e4f4454a1eea6d56f0398e7", "74c83bea924d470d99608f17cb74d338", "eb22c372a98e454e997cba3a562c7439", "197cf7905f7b493d90a4cf03b66cc0b9", "36acb5cf8816463ca36812913f2d163e", "730882a26b9741649831beaa86d76db2", "fcd47ca1590b43559d08c04f0bb4d164", "de744a456e7a49569eaa973837732c7b", "bf4518dfbee14600a799f300b00ab41b", "bd117e3f5a4240a982ff268c95fb2b4e", "55ddc088c8d149d584d5586680123f7a", "b4da694310f04f8fab4a9b64a526509e", "359825e9c7df4f01b54677b5b27f7916", "c8210da876ed4d6ab02d6c1897b10528", "631d08c360674a2b9b5aad1a388029fd", "8c5818ee930e49fdaeb6249a35bbca34", "5791332747c644049b9de417a3369818", "d164013a0ad24027a96f98887fd46d6a", "edeb62ff425d429cb6cc6de5fe20e68c", "b34db738e74a4f3dbc52f42dedc6dd8a", "c7b90e0210b44a838cc785acdc4d1298", "833923bb415e4d64b3d7d4e8fbcbbd11", "bf569df5a1e543f5b2617d2f5778a6a4", "a69de83c36cc46289a00ee81f4125cfa", "aa5dae53b5534d2abe0467a3f9d52008", "511fb228975e48a3b4a31bbf73cc2b42", "fffa059ea84f420fbd8940376678b431", "0593f018fea244b6907aa06164dd59c5"]}, "id": "TzUccPzV9ZE2", "outputId": "f71a86aa-619a-4914-c226-9ed23996e4ca"}, "outputs": [], "source": ["# !pip install -q -U huggingface_hub\n", "# from huggingface_hub import login\n", "\n", "# login()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Login to Weight&Biases (optional)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can use Weights&Biases to log the training process. This step is optional."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"metadata": {}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m25.1.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.2\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[34m\u001b[1mwandb\u001b[0m: Currently logged in as: \u001b[33mjohn<PERSON>uhoshim\u001b[0m (\u001b[33mjohngyuhoshim-university-of-wisconsin-madison\u001b[0m) to \u001b[32mhttps://api.wandb.ai\u001b[0m. Use \u001b[1m`wandb login --relogin`\u001b[0m to force relogin\n"]}], "source": ["if wandb_experiment_name:\n", "    !pip install -q -U wandb\n", "    import wandb\n", "\n", "    wandb.login()"]}, {"cell_type": "markdown", "metadata": {"id": "KMj35oGIErey"}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "Pr60gcNw7ORF", "metadata": {}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPU memory occupied: 24030 MB.\n"]}], "source": ["from pathlib import Path\n", "from typing import cast\n", "\n", "import torch\n", "from colpali_engine.collators.visual_retriever_collator import VisualRetrieverCollator\n", "from colpali_engine.loss import ColbertPairwiseCELoss\n", "from colpali_engine.models import ColPali, ColPaliProcessor\n", "from colpali_engine.trainer.contrastive_trainer import ContrastiveTrainer\n", "from colpali_engine.utils.torch_utils import get_torch_device, tear_down_torch\n", "from datasets import DatasetDict, load_dataset\n", "from peft import LoraConfig\n", "from torch import nn\n", "from transformers import BitsAndBytesConfig, TrainerCallback, TrainingArguments\n", "\n", "\n", "def print_trainable_parameters(model: nn.Mo<PERSON>) -> None:\n", "    \"\"\"\n", "    Print the number of trainable parameters in the model.\n", "    \"\"\"\n", "    trainable_params = 0\n", "    all_param = 0\n", "    for _, param in model.named_parameters():\n", "        all_param += param.numel()\n", "        if param.requires_grad:\n", "            trainable_params += param.numel()\n", "\n", "    trainable_percentage = 100 * trainable_params / all_param\n", "    print(f\"trainable params: {trainable_params:,} || all params: {all_param:,} || trainable%: {trainable_percentage}\")"]}, {"cell_type": "markdown", "metadata": {"id": "FiUP8tC67ORF"}, "source": ["## Choose a quantization strategy"]}, {"cell_type": "markdown", "metadata": {}, "source": ["ColPali is quite a large model with 3B parameters. While you can load the model and run inference on a L4 GPU or a M1+ Mac (in BF16, you'll need ≈6GB of VRAM), you will need much more VRAM to train it when taking into account the gradients and the AdamW optimizer states (≈48GB).\n", "\n", "Therefore, we will use LoRA to limit the number of trainable parameters (like how the original ColPali was trained).\n", "\n", "Even with LoRA, you might struggle to train ColPali on consumer GPUs. This is because of the contrastive loss used in ColPali: the larger the batch size, the more VRAM is used. Thus, we need to quantize the model to further reduce its memory footprint. Hence, we recommend using the 4-bit quantization with LoRA, i.e. QLoRA.\n", "\n", "See this [🤗 blog post](https://huggingface.co/docs/transformers/main/en/quantization/overview) for more information on quantization and this [🤗 blog post](https://huggingface.co/blog/4bit-transformers-bitsandbytes) for more details on QLoRA."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 542, "referenced_widgets": ["8fd70d5c36254bc3a79ac2d89212fe95", "dc7853a136234b3baafc99ba4e6376dd", "8fa1c9d926a34a5a92332881c487ccdb", "c3c8693b555c4145918078d870a2a648", "6f071f6dbae84a418e3cc8af8893f921", "18490330921d463ba5166f201ad39aca", "a6292478607641efa50af2909b4b769a", "1b3e5d1b083c47f09561f7537c0a1c6a", "93cf11b23d334dcc982c7e3ebd3ba57a", "4dc87365768a45678a4d32eac16188c5", "a8b6913554c74eb19a319cced459a8f6"]}, "id": "xckrnj6N7ORF", "metadata": {}, "outputId": "44afc9e9-d0c4-4ded-985a-213171736279"}, "outputs": [], "source": ["# ==========================     USER INPUT     ==========================\n", "\n", "QUANTIZATION_STRATEGY = \"4bit\"\n", "\n", "# ========================================================================\n", "\n", "# Automatically set the device\n", "device = get_torch_device(\"auto\")\n", "\n", "if QUANTIZATION_STRATEGY and device != \"cuda:0\":\n", "    raise ValueError(\"This notebook requires a CUDA GPU to use quantization.\")\n", "\n", "# Prepare quantization config\n", "if QUANTIZATION_STRATEGY is None:\n", "    bnb_config = None\n", "elif <PERSON>ATION_STRATEGY == \"8bit\":\n", "    bnb_config = BitsAndBytesConfig(\n", "        load_in_8bit=True,\n", "    )\n", "elif <PERSON>ATION_STRATEGY == \"4bit\":\n", "    bnb_config = BitsAndBytesConfig(\n", "        load_in_4bit=True,\n", "        bnb_4bit_quant_type=\"nf4\",\n", "        bnb_4bit_compute_dtype=torch.bfloat16,\n", "    )\n", "else:\n", "    raise ValueError(f\"Invalid quantization strategy: {QUANTIZATION_STRATEGY}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load the pre-trained model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For simplicity, we will continue training the LoRA adapter from the original ColPali model."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"metadata": {}}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7d3b7e91798f4a76bbbd8e7e1da79321", "version_major": 2, "version_minor": 0}, "text/plain": ["Fetching 2 files:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7a947229e3d64dc2b58dcbc383e9c4f0", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["trainable params: 39,292,928 || all params: 1,766,287,216 || trainable%: 2.224605808390791\n"]}], "source": ["# Pre-trained model name (with LoRA adapter)\n", "model_name = \"vidore/colpali-v1.2\"\n", "\n", "# Get the LoRA config from the pretrained model\n", "lora_config = LoraConfig.from_pretrained(model_name)\n", "\n", "# Load the model with the loaded pre-trained adapter\n", "model = cast(\n", "    <PERSON><PERSON><PERSON>,\n", "    ColPali.from_pretrained(\n", "        model_name,\n", "        quantization_config=bnb_config,\n", "        torch_dtype=torch.bfloat16,\n", "        device_map=device,\n", "    ),\n", ")\n", "\n", "if not model.active_adapters():\n", "    raise ValueError(\"No adapter found in the model.\")\n", "\n", "# The LoRA weights are frozen by default. We need to unfreeze them to fine-tune the model.\n", "for name, param in model.named_parameters():\n", "    if \"lora\" in name:\n", "        param.requires_grad = True\n", "\n", "print_trainable_parameters(model)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Additional note:** It is possible to train a new LoRA adapter on top of the existing one. This allows to have extra flexibility in the LoRA config and to faciltate adapter hot-swapping.\n", "\n", "To do this:\n", "\n", "1. Load the version of ColPali with the adapter already merged with the base model weights: `model = ColPali.from_pretrained(\"vidore/colpali-v1.2-merged\")`.\n", "2. Define your LoRA adapter using `lora_config = LoraConfig(...)`.\n", "3. Use `model = get_peft_model(model, lora_config)` to get the model with the new adapter.\n", "4. Run fine-tuning as usual."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load the processor and the collator"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "S0NX3fAx_g1U"}, "outputs": [], "source": ["if lora_config.base_model_name_or_path is None:\n", "    raise ValueError(\"Base model name or path is required in the LoRA config.\")\n", "\n", "processor = cast(\n", "    ColPaliProcessor,\n", "    ColPaliProcessor.from_pretrained(model_name),\n", ")\n", "collator = VisualRetrieverCollator(processor=processor)"]}, {"cell_type": "markdown", "metadata": {"id": "t2iY6U_6_g1U"}, "source": ["## Load the dataset"]}, {"cell_type": "markdown", "metadata": {}, "source": ["VDSID-French is a subset of the [`vidore/vdsid`](https://huggingface.co/datasets/vidore/vdsid). It contains 5000 document-question-answer triplet of French documets, split into a train set of 4700 examples and a test set of 300 examples.\n", "\n", "This dataset was created and chosen for this fine-tuning because ColPali was mainly trained on English documents. Thus fine-tuning on French documents can help to improve the multilingual capabilities of the model."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "RY7sVT8q_g1U"}, "outputs": [{"data": {"text/plain": ["DatasetDict({\n", "    train: Dataset({\n", "        features: ['document_filename', 'document_url', 'search_query', 'search_topic', 'search_subtopic', 'search_language', 'search_filetype', 'page_number', 'page_description', 'page_language', 'page_contains_table', 'page_contains_figure', 'page_contains_paragraph', 'image', 'query_type', 'query_answerability', 'query_modality', 'query_language', 'query_reasoning', 'query', 'query_is_self_contained', 'query_is_self_contained_reasoning', 'answer'],\n", "        num_rows: 4700\n", "    })\n", "    test: Dataset({\n", "        features: ['document_filename', 'document_url', 'search_query', 'search_topic', 'search_subtopic', 'search_language', 'search_filetype', 'page_number', 'page_description', 'page_language', 'page_contains_table', 'page_contains_figure', 'page_contains_paragraph', 'image', 'query_type', 'query_answerability', 'query_modality', 'query_language', 'query_reasoning', 'query', 'query_is_self_contained', 'query_is_self_contained_reasoning', 'answer'],\n", "        num_rows: 300\n", "    })\n", "})"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the dataset\n", "dataset_name = \"vidore/vdsid_french\"\n", "ds = cast(DatasetDict, load_dataset(dataset_name))\n", "\n", "# Rename the columns to match the trainer's requirements\n", "ds = ds.rename_column(\"page_image\", \"image\")\n", "ds[\"train\"] = ds[\"train\"].shuffle(seed=42)\n", "\n", "ds"]}, {"cell_type": "markdown", "metadata": {"id": "1oiBwyc6_g1V"}, "source": ["## Define training args"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Depending on your hardware, you might need to adjust the train batch size. This parameter is crucial for the training of ColPali as it is trained with a contrastive loss: the larger the batch size, the more representative the negative samples are, and the better the model will perform."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "Wp0x1ZcG_g1V"}, "outputs": [], "source": ["checkpoints_dir = Path(\"checkpoints\")\n", "checkpoints_dir.mkdir(exist_ok=True, parents=True)\n", "\n", "training_args = TrainingArguments(\n", "    output_dir=str(checkpoints_dir),\n", "    hub_model_id=hf_pushed_model_name if hf_pushed_model_name else None,\n", "    overwrite_output_dir=True,\n", "    num_train_epochs=1.5,\n", "    per_device_train_batch_size=4,\n", "    per_device_eval_batch_size=4,\n", "    gradient_accumulation_steps=4,\n", "    gradient_checkpointing=False,\n", "    eval_strategy=\"steps\",\n", "    save_steps=200,\n", "    logging_steps=20,\n", "    eval_steps=100,\n", "    warmup_steps=100,\n", "    learning_rate=5e-5,\n", "    save_total_limit=1,\n", "    report_to=[\"wandb\"] if wandb_experiment_name else [],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create the trainer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The trainer uses a ColBERT contrastive hard-margin loss. Read the [ColPali paper](https://doi.org/10.48550/arXiv.2407.01449) for more details on this loss function."]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 492}, "id": "bNw9fDvb_g1V", "outputId": "6b049ca9-e82b-4df6-f628-7a007ae52aa3"}, "outputs": [], "source": ["class EvaluateFirstStepCallback(TrainerCallback):\n", "    \"\"\"\n", "    Run eval after the first training step.\n", "    Used to have a more precise evaluation learning curve.\n", "    \"\"\"\n", "\n", "    def on_step_end(self, args, state, control, **kwargs):\n", "        if state.global_step == 1:\n", "            control.should_evaluate = True\n", "\n", "\n", "trainer = ContrastiveTrainer(\n", "    model=model,\n", "    train_dataset=ds[\"train\"],\n", "    eval_dataset=ds[\"test\"],\n", "    args=training_args,\n", "    data_collator=collator,\n", "    loss_func=ColbertPairwiseCELoss(),\n", "    is_vision_model=True,\n", ")\n", "\n", "trainer.args.remove_unused_columns = False\n", "trainer.add_callback(EvaluateFirstStepCallback())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluate the model before training"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's see how <PERSON><PERSON><PERSON> performs on the test set prior to fine-tuning."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='150' max='75' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [75/75 01:26]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[34m\u001b[1mwandb\u001b[0m: \u001b[33mWARNING\u001b[0m The `run_name` is currently set to the same value as `TrainingArguments.output_dir`. If this was not intended, please specify a different run name by setting the `TrainingArguments.run_name` parameter.\n"]}, {"data": {"text/html": ["Tracking run with wandb version 0.18.1"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["Run data is saved locally in <code>/home/<USER>/sky_workdir/examples/wandb/run-20240925_075620-1v8bharg</code>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["Syncing run <strong><a href='https://wandb.ai/tonywu_71/huggingface/runs/1v8bharg' target=\"_blank\">checkpoints</a></strong> to <a href='https://wandb.ai/tonywu_71/huggingface' target=\"_blank\">Weights & Biases</a> (<a href='https://wandb.me/run' target=\"_blank\">docs</a>)<br/>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [" View project at <a href='https://wandb.ai/tonywu_71/huggingface' target=\"_blank\">https://wandb.ai/tonywu_71/huggingface</a>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [" View run at <a href='https://wandb.ai/tonywu_71/huggingface/runs/1v8bharg' target=\"_blank\">https://wandb.ai/tonywu_71/huggingface/runs/1v8bharg</a>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["{'eval_loss': 0.04893038421869278,\n", " 'eval_model_preparation_time': 0.0121,\n", " 'eval_runtime': 41.4353,\n", " 'eval_samples_per_second': 7.24,\n", " 'eval_steps_per_second': 1.81}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["eval_results = trainer.evaluate()\n", "eval_results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fine-tune the model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Finally, time for fine-tuning! Run the following cell then go make yourself a cup of tea or take a walk while the model trains. 🚀 \n", "\n", "On a A100-40GB GPU and with the default training parameters, fine-tuning should take around 30 minutes. ⏱️"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["Finishing last run (ID:1v8bharg) before initializing another..."], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8fb8ab33e0664cf392f62ee539c08fb0", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(Label(value='0.032 MB of 0.032 MB uploaded\\r'), FloatProgress(value=1.0, max=1.0)))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style>\n", "    table.wandb td:nth-child(1) { padding: 0 10px; text-align: left ; width: auto;} td:nth-child(2) {text-align: left ; width: 100%}\n", "    .wandb-row { display: flex; flex-direction: row; flex-wrap: wrap; justify-content: flex-start; width: 100% }\n", "    .wandb-col { display: flex; flex-direction: column; flex-basis: 100%; flex: 1; padding: 10px; }\n", "    </style>\n", "<div class=\"wandb-row\"><div class=\"wandb-col\"><h3>Run history:</h3><br/><table class=\"wandb\"><tr><td>eval/loss</td><td>▁</td></tr><tr><td>eval/model_preparation_time</td><td>▁</td></tr><tr><td>eval/runtime</td><td>▁</td></tr><tr><td>eval/samples_per_second</td><td>▁</td></tr><tr><td>eval/steps_per_second</td><td>▁</td></tr><tr><td>train/global_step</td><td>▁</td></tr></table><br/></div><div class=\"wandb-col\"><h3>Run summary:</h3><br/><table class=\"wandb\"><tr><td>eval/loss</td><td>0.04893</td></tr><tr><td>eval/model_preparation_time</td><td>0.0121</td></tr><tr><td>eval/runtime</td><td>41.4353</td></tr><tr><td>eval/samples_per_second</td><td>7.24</td></tr><tr><td>eval/steps_per_second</td><td>1.81</td></tr><tr><td>train/global_step</td><td>0</td></tr></table><br/></div></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [" View run <strong style=\"color:#cdcd00\">checkpoints</strong> at: <a href='https://wandb.ai/tonywu_71/huggingface/runs/1v8bharg' target=\"_blank\">https://wandb.ai/tonywu_71/huggingface/runs/1v8bharg</a><br/> View project at: <a href='https://wandb.ai/tonywu_71/huggingface' target=\"_blank\">https://wandb.ai/tonywu_71/huggingface</a><br/>Synced 5 W&B file(s), 0 media file(s), 2 artifact file(s) and 0 other file(s)"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["Find logs at: <code>./wandb/run-20240925_075620-1v8bharg/logs</code>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["Successfully finished last run (ID:1v8bharg). Initializing new run:<br/>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["Tracking run with wandb version 0.18.1"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["Run data is saved locally in <code>/home/<USER>/sky_workdir/examples/wandb/run-20240925_075620-luqihcpc</code>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["Syncing run <strong><a href='https://wandb.ai/tonywu_71/colpali/runs/luqihcpc' target=\"_blank\">finetune_colpali_v1_2-vdsid_french-4bit</a></strong> to <a href='https://wandb.ai/tonywu_71/colpali' target=\"_blank\">Weights & Biases</a> (<a href='https://wandb.me/run' target=\"_blank\">docs</a>)<br/>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [" View project at <a href='https://wandb.ai/tonywu_71/colpali' target=\"_blank\">https://wandb.ai/tonywu_71/colpali</a>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [" View run at <a href='https://wandb.ai/tonywu_71/colpali/runs/luqihcpc' target=\"_blank\">https://wandb.ai/tonywu_71/colpali/runs/luqihcpc</a>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='440' max='440' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [440/440 31:58, Epoch 1/2]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "      <th>Model Preparation Time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>No log</td>\n", "      <td>0.048930</td>\n", "      <td>0.012100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>100</td>\n", "      <td>0.017100</td>\n", "      <td>0.021548</td>\n", "      <td>0.012100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>200</td>\n", "      <td>0.010000</td>\n", "      <td>0.030120</td>\n", "      <td>0.012100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>300</td>\n", "      <td>0.009000</td>\n", "      <td>0.031809</td>\n", "      <td>0.012100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>400</td>\n", "      <td>0.012000</td>\n", "      <td>0.031864</td>\n", "      <td>0.012100</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/sky_workdir/.venv/lib/python3.10/site-packages/transformers/integrations/peft.py:397: FutureWarning: The `active_adapter` method is deprecated and will be removed in a future version.\n", "  warnings.warn(\n", "/home/<USER>/sky_workdir/.venv/lib/python3.10/site-packages/transformers/models/paligemma/configuration_paligemma.py:137: FutureWarning: The `vocab_size` attribute is deprecated and will be removed in v4.44, Please use `text_config.vocab_size` instead.\n", "  warnings.warn(\n", "/home/<USER>/sky_workdir/.venv/lib/python3.10/site-packages/transformers/integrations/peft.py:397: FutureWarning: The `active_adapter` method is deprecated and will be removed in a future version.\n", "  warnings.warn(\n", "/home/<USER>/sky_workdir/.venv/lib/python3.10/site-packages/transformers/models/paligemma/configuration_paligemma.py:137: FutureWarning: The `vocab_size` attribute is deprecated and will be removed in v4.44, Please use `text_config.vocab_size` instead.\n", "  warnings.warn(\n", "/home/<USER>/sky_workdir/.venv/lib/python3.10/site-packages/transformers/integrations/peft.py:397: FutureWarning: The `active_adapter` method is deprecated and will be removed in a future version.\n", "  warnings.warn(\n", "/home/<USER>/sky_workdir/.venv/lib/python3.10/site-packages/transformers/models/paligemma/configuration_paligemma.py:137: FutureWarning: The `vocab_size` attribute is deprecated and will be removed in v4.44, Please use `text_config.vocab_size` instead.\n", "  warnings.warn(\n"]}, {"data": {"text/plain": ["TrainOutput(global_step=440, training_loss=0.022981878132982688, metrics={'train_runtime': 1922.3275, 'train_samples_per_second': 3.667, 'train_steps_per_second': 0.229, 'total_flos': 1.0598129422848e+17, 'train_loss': 0.022981878132982688, 'epoch': 1.4978723404255319})"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# Prepare WandB logging\n", "if wandb_experiment_name:\n", "    wandb_tags = [\"finetuning\", \"colpali\"]\n", "\n", "    if bnb_config:\n", "        wandb_tags.append(\"quantization\")\n", "\n", "    run = wandb.init(\n", "        project=\"colpali\",\n", "        name=wandb_experiment_name,\n", "        job_type=\"finetuning\",\n", "        tags=wandb_tags,\n", "        config={\n", "            \"model_name\": model_name,\n", "            \"bitsandbytes_config\": bnb_config.to_dict() if bnb_config else None,\n", "            \"dataset_name\": dataset_name,\n", "        },\n", "    )\n", "\n", "# Train the model\n", "train_results = trainer.train()\n", "\n", "train_results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluate the model after training"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's see how the fine-tuned model performs on the test set. You should observe a drop in the evaluation loss. \n", "\n", "To further evaluate the model, you can use the [`vidore-benchmark`](https://github.com/illuin-tech/vidore-benchmark) to measure the retrieval performance (e.g. NDCG@5)."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='75' max='75' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [75/75 00:39]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["{'eval_loss': 0.03164391964673996,\n", " 'eval_model_preparation_time': 0.0121,\n", " 'eval_runtime': 40.3053,\n", " 'eval_samples_per_second': 7.443,\n", " 'eval_steps_per_second': 1.861,\n", " 'epoch': 1.4978723404255319}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["eval_results = trainer.evaluate()\n", "eval_results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["During my own experiments, I got the following learning curves:\n", "\n", "<p align=\"center\"><img width=800 src=\"https://github.com/tonywu71/colpali-cookbooks/blob/main/assets/finetuning/learning_curves.jpeg?raw=true\"/></p>\n", "\n", "A few observations:\n", "\n", "- The training loss is globally decreasing, which proves that the model is learning.\n", "- The validation loss rapidly decreases for the first 100 steps, goes back up and starts plateauing after 200 steps. This is a sign of overfitting, so we probably should have stopped the training a bit earlier."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclude the WandB run (optional)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6d7de631e6ce44aaa9a5876b16d5a497", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(Label(value='0.003 MB of 0.003 MB uploaded\\r'), FloatProgress(value=1.0, max=1.0)))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style>\n", "    table.wandb td:nth-child(1) { padding: 0 10px; text-align: left ; width: auto;} td:nth-child(2) {text-align: left ; width: 100%}\n", "    .wandb-row { display: flex; flex-direction: row; flex-wrap: wrap; justify-content: flex-start; width: 100% }\n", "    .wandb-col { display: flex; flex-direction: column; flex-basis: 100%; flex: 1; padding: 10px; }\n", "    </style>\n", "<div class=\"wandb-row\"><div class=\"wandb-col\"><h3>Run history:</h3><br/><table class=\"wandb\"><tr><td>eval/loss</td><td>█▁▃▄▄▄</td></tr><tr><td>eval/model_preparation_time</td><td>▁▁▁▁▁▁</td></tr><tr><td>eval/runtime</td><td>▅▂▁▃▁█</td></tr><tr><td>eval/samples_per_second</td><td>▄▇█▅█▁</td></tr><tr><td>eval/steps_per_second</td><td>▄▇█▅█▁</td></tr><tr><td>train/epoch</td><td>▁▁▂▂▂▃▃▃▃▄▄▄▄▄▅▅▅▆▆▆▆▇▇▇▇████</td></tr><tr><td>train/global_step</td><td>▁▁▂▂▂▃▃▃▃▄▄▄▄▄▅▅▅▆▆▆▆▇▇▇▇████</td></tr><tr><td>train/grad_norm</td><td>▂▁█▂▁▁▂▆▁▁▁▁▁▁▂▁▁▁▁▁▁▁</td></tr><tr><td>train/learning_rate</td><td>▂▄▅▇██▇▇▆▆▆▅▅▄▄▃▃▃▂▂▁▁</td></tr><tr><td>train/loss</td><td>▇█▆▃▃▂▄▇▅▂▃█▂▄▂▂▂▂▁▂▂▁</td></tr></table><br/></div><div class=\"wandb-col\"><h3>Run summary:</h3><br/><table class=\"wandb\"><tr><td>eval/loss</td><td>0.03164</td></tr><tr><td>eval/model_preparation_time</td><td>0.0121</td></tr><tr><td>eval/runtime</td><td>40.3053</td></tr><tr><td>eval/samples_per_second</td><td>7.443</td></tr><tr><td>eval/steps_per_second</td><td>1.861</td></tr><tr><td>total_flos</td><td>1.0598129422848e+17</td></tr><tr><td>train/epoch</td><td>1.49787</td></tr><tr><td>train/global_step</td><td>440</td></tr><tr><td>train/grad_norm</td><td>0.02536</td></tr><tr><td>train/learning_rate</td><td>0</td></tr><tr><td>train/loss</td><td>0.0024</td></tr><tr><td>train_loss</td><td>0.02298</td></tr><tr><td>train_runtime</td><td>1922.3275</td></tr><tr><td>train_samples_per_second</td><td>3.667</td></tr><tr><td>train_steps_per_second</td><td>0.229</td></tr></table><br/></div></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": [" View run <strong style=\"color:#cdcd00\">finetune_colpali_v1_2-vdsid_french-4bit</strong> at: <a href='https://wandb.ai/tonywu_71/colpali/runs/luqihcpc' target=\"_blank\">https://wandb.ai/tonywu_71/colpali/runs/luqihcpc</a><br/> View project at: <a href='https://wandb.ai/tonywu_71/colpali' target=\"_blank\">https://wandb.ai/tonywu_71/colpali</a><br/>Synced 5 W&B file(s), 0 media file(s), 2 artifact file(s) and 0 other file(s)"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["Find logs at: <code>./wandb/run-20240925_075620-luqihcpc/logs</code>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if wandb_experiment_name:\n", "    run.finish()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Push the model to the Hub (optional)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If satisfied with the fine-tuned model, you can push it to the Hub to share it with the community! 😍\n", "\n", "You can find my fine-tuned ColPali model for reference at [`tonywu71/finetune_colpali_v1_2-vdsid_french-4bit`](https://huggingface.co/tonywu71/finetune_colpali_v1_2-vdsid_french-4bit). For more inspiration, check out the [🤗 Hf Hub](https://huggingface.co/models?other=base_model:finetune:vidore/colpaligemma-3b-pt-448-base) to see the ColPali models that the community has already fine-tuned."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/sky_workdir/.venv/lib/python3.10/site-packages/transformers/integrations/peft.py:397: FutureWarning: The `active_adapter` method is deprecated and will be removed in a future version.\n", "  warnings.warn(\n", "/home/<USER>/sky_workdir/.venv/lib/python3.10/site-packages/transformers/models/paligemma/configuration_paligemma.py:137: FutureWarning: The `vocab_size` attribute is deprecated and will be removed in v4.44, Please use `text_config.vocab_size` instead.\n", "  warnings.warn(\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d82a70ffb58548e9990a697d25103f10", "version_major": 2, "version_minor": 0}, "text/plain": ["Upload 2 LFS files:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0f2bc22225df46078f6a1104a69c7371", "version_major": 2, "version_minor": 0}, "text/plain": ["adapter_model.safetensors:   0%|          | 0.00/157M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "17093ae6a9f84c02a7c7d794c22bebdc", "version_major": 2, "version_minor": 0}, "text/plain": ["training_args.bin:   0%|          | 0.00/5.24k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if hf_pushed_model_name:\n", "    trainer.push_to_hub(tags=[\"colpali\"], dataset=dataset_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Congrats, you have successfully fine-tuned ColPali! 🎉"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load your fine-tuned model (optional)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To use your freshly fine-tuned model, use the code from the following cell to load your own ColPali in your own project. 🫶🏼"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "782f6ec31cc04742addc8fc8b47238e3", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Unload the previous model and clean the GPU cache\n", "del model\n", "tear_down_torch()\n", "\n", "# Load your fine-tuned ColPali\n", "model = ColPali.from_pretrained(\n", "    model_name,\n", "    torch_dtype=torch.bfloat16,\n", "    device_map=device,\n", ")"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}