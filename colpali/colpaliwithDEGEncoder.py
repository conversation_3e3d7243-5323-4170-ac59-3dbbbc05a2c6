# coding=utf-8
# Copyright 2024 The HuggingFace Inc. team.
"""Dynamic Grained Encoder replacement for ColPali Vision Encoder"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Union, Tuple
from dataclasses import dataclass

from transformers import AutoModelForImageTextToText
from transformers.models.paligemma.processing_paligemma import PaliGemmaProcessor
from transformers.models.colpali.configuration_colpali import ColPaliConfig
from transformers.models.colpali.modeling_colpali import ColPaliForRetrievalOutput
from ...feature_extraction_utils import BatchFeature
from ...modeling_utils import PreTrainedModel
from ...utils import ModelOutput

class DynamicGrainedRouter(nn.Module):
    """
    Dynamic Grained Router from NeurIPS 2021 paper
    'Dynamic Grained Encoder for Vision Transformers'
    """
    def __init__(self, 
                 budget: float = 0.5,
                 window_size: int = 14,  # Match ViT patch size
                 granularities: list = [1, 2, 4, 8],
                 hidden_dim: int = 768):
        super().__init__()
        self.budget = budget
        self.window_size = window_size
        self.granularities = granularities
        self.hidden_dim = hidden_dim
        
        # Gating network for granularity selection
        self.gating_network = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(hidden_dim, 256),
            nn.ReLU(),
            nn.Linear(256, len(granularities)),
            nn.Softmax(dim=-1)
        )
        
        # Patch embedding layer
        self.patch_embed = nn.Conv2d(3, hidden_dim, kernel_size=window_size, stride=window_size)
        
        # Positional embedding (learned)
        self.pos_embed = nn.Parameter(torch.randn(1, 1024, hidden_dim) * 0.02)
        
    def forward(self, pixel_values: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            pixel_values: Input images of shape (B, C, H, W)
            
        Returns:
            encoded_patches: Dynamic patches (B, N_patches, hidden_dim)
            granularity_map: Granularity assignments (B, num_windows_h, num_windows_w)
        """
        B, C, H, W = pixel_values.shape
        
        # Ensure divisibility by window size
        assert H % self.window_size == 0 and W % self.window_size == 0, \
            f"Image dimensions ({H}, {W}) must be divisible by window_size ({self.window_size})"
        
        num_windows_h = H // self.window_size
        num_windows_w = W // self.window_size
        
        # Initial patch embedding
        patch_features = self.patch_embed(pixel_values)  # (B, hidden_dim, num_windows_h, num_windows_w)
        patch_features = patch_features.permute(0, 2, 3, 1)  # (B, num_windows_h, num_windows_w, hidden_dim)
        
        # Flatten for gating network
        flat_features = patch_features.view(-1, self.hidden_dim)  # (B*num_windows_h*num_windows_w, hidden_dim)
        
        # Compute granularity weights
        granularity_weights = self.gating_network(flat_features.view(-1, self.hidden_dim, 1, 1))
        granularity_assignments = torch.argmax(granularity_weights, dim=-1)
        
        # Reshape granularity assignments
        granularity_map = granularity_assignments.view(B, num_windows_h, num_windows_w)
        
        # Apply dynamic granularity with budget constraint
        encoded_patches = self._apply_dynamic_granularity(
            pixel_values, granularity_map, num_windows_h, num_windows_w
        )
        
        # Add positional embeddings
        seq_len = encoded_patches.shape[1]
        pos_embed = self.pos_embed[:, :seq_len, :]
        encoded_patches = encoded_patches + pos_embed
        
        return encoded_patches, granularity_map
    
    def _apply_dynamic_granularity(self, pixel_values, granularity_map, num_windows_h, num_windows_w):
        """Apply different granularities based on gating decisions"""
        B, C, H, W = pixel_values.shape
        all_patches = []
        
        for b in range(B):
            batch_patches = []
            
            for h in range(num_windows_h):
                for w in range(num_windows_w):
                    # Extract window region
                    h_start, h_end = h * self.window_size, (h + 1) * self.window_size
                    w_start, w_end = w * self.window_size, (w + 1) * self.window_size
                    window = pixel_values[b:b+1, :, h_start:h_end, w_start:w_end]
                    
                    # Get granularity for this window
                    granularity = self.granularities[granularity_map[b, h, w].item()]
                    
                    # Apply granularity-specific processing
                    if granularity == 1:
                        # Finest granularity - use original patches
                        window_patches = self.patch_embed(window).flatten(2).transpose(1, 2)
                    else:
                        # Coarser granularity - pool then embed
                        pooled_size = self.window_size // granularity
                        pooled = F.adaptive_avg_pool2d(window, (pooled_size, pooled_size))
                        window_patches = self.patch_embed(pooled).flatten(2).transpose(1, 2)
                    
                    batch_patches.append(window_patches.squeeze(0))
            
            # Concatenate all patches for this batch item
            batch_patches = torch.cat(batch_patches, dim=0)
            all_patches.append(batch_patches)
        
        # Pad to consistent length (1024 for ColPali compatibility)
        max_patches = 1024
        padded_patches = []
        
        for patches in all_patches:
            if patches.shape[0] > max_patches:
                # Downsample if too many patches
                indices = torch.linspace(0, patches.shape-1, max_patches, dtype=torch.long)
                patches = patches[indices]
            elif patches.shape < max_patches:
                # Pad if too few patches
                padding = torch.zeros(max_patches - patches.shape, patches.shape[1], 
                                    device=patches.device, dtype=patches.dtype)
                patches = torch.cat([patches, padding], dim=0)
            
            padded_patches.append(patches)
        
        return torch.stack(padded_patches)

class DynamicGrainedVisionTransformer(nn.Module):
    """Vision Transformer with Dynamic Grained Encoder"""
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        
        # Dynamic Grained Router
        self.dge_router = DynamicGrainedRouter(
            budget=0.5,
            window_size=14,  # Standard ViT patch size
            granularities=[1, 2, 4, 8],
            hidden_dim=768
        )
        
        # Transformer layers (reuse from original vision model)
        # For simplicity, we'll use a basic transformer
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=768,
            nhead=12,
            dim_feedforward=3072,
            dropout=0.1,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=12)
        
        # Layer norm
        self.layer_norm = nn.LayerNorm(768)
        
    def forward(self, pixel_values, **kwargs):
        """Forward pass through DGE Vision Transformer"""
        # Apply dynamic grained encoding
        patch_embeddings, granularity_map = self.dge_router(pixel_values)
        
        # Pass through transformer layers
        hidden_states = self.transformer(patch_embeddings)
        
        # Final layer norm
        hidden_states = self.layer_norm(hidden_states)
        
        return hidden_states, granularity_map

class ColPaliWithDGEEncoder(PreTrainedModel):
    """ColPali model with Dynamic Grained Encoder replacing the vision encoder"""
    
    config_class = ColPaliConfig
    
    def __init__(self, config: ColPaliConfig):
        super().__init__(config)
        self.config = config
        
        # Replace vision encoder with DGE
        self.vision_model = DynamicGrainedVisionTransformer(config)
        
        # Language model (keep original)
        self.vlm = AutoModelForImageTextToText.from_config(config.vlm_config)
        
        # Embedding projection layer
        self.embedding_dim = config.embedding_dim
        self.embedding_proj_layer = nn.Linear(
            768,  # DGE output dimension
            self.embedding_dim
        )
        
        self.post_init()
    
    def forward(
        self,
        input_ids: Optional[torch.LongTensor] = None,
        pixel_values: Optional[torch.FloatTensor] = None,
        attention_mask: Optional[torch.Tensor] = None,
        output_attentions: Optional[bool] = None,
        output_hidden_states: Optional[bool] = None,
        return_dict: Optional[bool] = None,
        **kwargs,
    ) -> ColPaliForRetrievalOutput:
        
        if pixel_values is not None:
            pixel_values = pixel_values.to(dtype=self.dtype)
        
        return_dict = return_dict if return_dict is not None else self.config.use_return_dict
        
        if pixel_values is not None:
            # Process images through DGE
            vision_outputs, granularity_map = self.vision_model(pixel_values)
            
            # For text-only processing, use language model
            if input_ids is not None:
                # Combine vision and text (simplified - proper multimodal fusion needed)
                # This is a placeholder for proper VLM integration
                text_outputs = self.vlm.model.language_model(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    **kwargs
                )
                
                # Concatenate vision and text embeddings
                combined_outputs = torch.cat([vision_outputs, text_outputs.last_hidden_state], dim=1)
                last_hidden_states = combined_outputs
            else:
                last_hidden_states = vision_outputs
        else:
            # Text-only processing
            vlm_output = self.vlm.model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                **kwargs
            )
            last_hidden_states = vlm_output[0]
        
        # Project to embedding dimension
        embeddings = self.embedding_proj_layer(last_hidden_states)
        
        # L2 normalization
        embeddings = embeddings / embeddings.norm(dim=-1, keepdim=True)
        
        if attention_mask is not None:
            embeddings = embeddings * attention_mask.unsqueeze(-1)
        
        return ColPaliForRetrievalOutput(
            embeddings=embeddings,
            hidden_states=(last_hidden_states,) if output_hidden_states else None,
        )

class DynamicColPaliProcessor(PaliGemmaProcessor):
    """Enhanced ColPali processor for Dynamic Grained Encoder"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.visual_prompt_prefix = "Describe the image."
        self.query_prefix = "Question: "
    
    def process_images(self, images, **kwargs):
        """Process images for DGE-enhanced ColPali"""
        # Ensure images are properly sized for DGE (must be divisible by patch size)
        processed = super().__call__(images=images, **kwargs)
        
        # Ensure pixel values are properly shaped
        pixel_values = processed['pixel_values']
        B, C, H, W = pixel_values.shape
        
        # Resize if necessary to be divisible by 14 (patch size)
        if H % 14 != 0 or W % 14 != 0:
            new_H = ((H // 14) + 1) * 14
            new_W = ((W // 14) + 1) * 14
            pixel_values = F.interpolate(pixel_values, size=(new_H, new_W), mode='bilinear', align_corners=False)
            processed['pixel_values'] = pixel_values
        
        return processed

# Usage Example
def main():
    """Example usage of ColPali with Dynamic Grained Encoder"""
    
    # Create configuration
    config = ColPaliConfig(embedding_dim=128)
    
    # Initialize model with DGE
    model = ColPaliWithDGEEncoder(config)
    model.eval()
    
    # Initialize processor
    processor = DynamicColPaliProcessor.from_pretrained("google/paligemma-3b-pt-224")
    
    # Example image processing
    import torch
    from PIL import Image
    
    # Create dummy image
    dummy_image = Image.new('RGB', (224, 224), color='white')
    
    # Process image
    inputs = processor.process_images([dummy_image])
    
    # Forward pass
    with torch.no_grad():
        outputs = model(**inputs)
    
    print(f"Output embeddings shape: {outputs.embeddings.shape}")
    print("Dynamic Grained Encoder integration successful!")

if __name__ == "__main__":
    main()
