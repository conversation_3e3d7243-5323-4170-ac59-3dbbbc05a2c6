import torch
import numpy as np
from PIL import Image
from datasets import load_dataset
from torch.utils.data import DataLoader
from collections import defaultdict
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple, Optional
import json

class ColPaliVisualProber:
    """
    Comprehensive probing framework to evaluate ColPali's performance on 
    documents with visual elements (charts, tables, graphs).
    """
    
    def __init__(self, model, processor, device='cuda'):
        self.model = model
        self.processor = processor
        self.device = device
        self.model.to(device)
        self.model.eval()
        
    def load_vidore_dataset(self, dataset_name="vidore/vidore-benchmark"):
        """Load ViDoRe benchmark dataset"""
        try:
            dataset = load_dataset(dataset_name)
            return dataset['test']  # Use test split
        except Exception as e:
            print(f"Error loading dataset: {e}")
            return None
    
    def categorize_documents_by_visual_content(self, dataset):
        """
        Categorize documents based on visual content complexity.
        This is a heuristic approach - you might need domain-specific logic.
        """
        categories = {
            'text_heavy': [],      # Mostly text
            'visual_rich': [],     # Contains charts/tables/graphs
            'mixed_content': []    # Balanced text and visual
        }
        
        for i, item in enumerate(dataset):
            # You can implement visual content detection here
            # For now, using placeholder logic based on query keywords
            query = item.get('query', '').lower()
            
            if any(keyword in query for keyword in ['chart', 'graph', 'table', 'figure', 'diagram']):
                categories['visual_rich'].append(i)
            elif any(keyword in query for keyword in ['text', 'paragraph', 'section', 'word']):
                categories['text_heavy'].append(i)
            else:
                categories['mixed_content'].append(i)
                
        return categories
    
    def extract_embeddings(self, items: List[Dict], mode='document') -> torch.Tensor:
        """Extract embeddings for documents or queries"""
        embeddings = []
        
        with torch.no_grad():
            batch_size = 4  # Adjust based on GPU memory
            for i in range(0, len(items), batch_size):
                batch = items[i:i + batch_size]
                
                if mode == 'document':
                    # Process document images
                    images = [item['document_image'] for item in batch]
                    inputs = self.processor.process_images(images=images, return_tensors="pt")
                else:
                    # Process queries
                    queries = [item['query'] for item in batch]
                    inputs = self.processor.process_queries(text=queries, return_tensors="pt")
                
                inputs = {k: v.to(self.device) for k, v in inputs.items()}
                outputs = self.model(**inputs)
                
                # Use the full multi-vector embeddings for late interaction
                # Shape: (batch_size, sequence_length, embedding_dim)
                batch_embeddings = outputs.embeddings.cpu()
                embeddings.append(batch_embeddings)
        
        return torch.cat(embeddings, dim=0)
    
    def compute_late_interaction_scores(self, query_embeddings, doc_embeddings):
        """
        Compute ColBERT-style late interaction scores between queries and documents
        """
        scores = []
        
        for q_emb in query_embeddings:  # Shape: (q_seq_len, emb_dim)
            doc_scores = []
            for d_emb in doc_embeddings:  # Shape: (d_seq_len, emb_dim)
                # Compute similarity matrix: (q_seq_len, d_seq_len)
                sim_matrix = torch.matmul(q_emb, d_emb.T)
                # MaxSim operation: for each query token, take max similarity with any doc token
                max_sim = sim_matrix.max(dim=1)[0]  # Shape: (q_seq_len,)
                # Sum over query tokens
                score = max_sim.sum().item()
                doc_scores.append(score)
            scores.append(doc_scores)
        
        return torch.tensor(scores)  # Shape: (num_queries, num_docs)
    
    def evaluate_retrieval_performance(self, dataset, categories=None):
        """
        Main evaluation function that computes retrieval metrics
        """
        results = {
            'overall': {},
            'by_category': {}
        }
        
        if categories is None:
            categories = self.categorize_documents_by_visual_content(dataset)
        
        # Extract embeddings for all documents and queries
        print("Extracting document embeddings...")
        doc_embeddings = self.extract_embeddings(dataset, mode='document')
        
        print("Extracting query embeddings...")
        query_embeddings = self.extract_embeddings(dataset, mode='query')
        
        print("Computing late interaction scores...")
        similarity_scores = self.compute_late_interaction_scores(query_embeddings, doc_embeddings)
        
        # Compute overall metrics
        overall_metrics = self._compute_metrics(similarity_scores, list(range(len(dataset))))
        results['overall'] = overall_metrics
        
        # Compute metrics by category
        for category, indices in categories.items():
            if len(indices) > 0:
                category_scores = similarity_scores[indices][:, indices]  # Subset to category
                category_metrics = self._compute_metrics(category_scores, list(range(len(indices))))
                results['by_category'][category] = category_metrics
        
        return results
    
    def _compute_metrics(self, similarity_scores, valid_indices, k_values=[1, 5, 10, 20]):
        """Compute Recall@k and MRR metrics"""
        metrics = {}
        num_queries = len(valid_indices)
        
        recalls = {k: 0 for k in k_values}
        reciprocal_ranks = []
        
        for i, query_idx in enumerate(valid_indices):
            # Get similarity scores for this query
            query_scores = similarity_scores[i]
            
            # Sort documents by similarity (descending)
            sorted_indices = torch.argsort(query_scores, descending=True)
            
            # Find position of correct document (assuming query i matches doc i)
            correct_doc_idx = i
            try:
                rank = (sorted_indices == correct_doc_idx).nonzero(as_tuple=True)[0][0].item() + 1
                reciprocal_ranks.append(1.0 / rank)
                
                # Check Recall@k
                for k in k_values:
                    if rank <= k:
                        recalls[k] += 1
            except IndexError:
                # Document not found in top results
                reciprocal_ranks.append(0.0)
        
        # Calculate final metrics
        for k in k_values:
            metrics[f'Recall@{k}'] = recalls[k] / num_queries
        
        metrics['MRR'] = sum(reciprocal_ranks) / len(reciprocal_ranks)
        
        return metrics
    
    def run_comprehensive_probe(self, dataset_name="vidore/vidore-benchmark"):
        """
        Run the complete probing experiment
        """
        print("Loading dataset...")
        dataset = self.load_vidore_dataset(dataset_name)
        if dataset is None:
            return None
        
        print("Categorizing documents...")
        categories = self.categorize_documents_by_visual_content(dataset)
        
        print("Category distribution:")
        for cat, indices in categories.items():
            print(f"  {cat}: {len(indices)} documents")
        
        print("\nEvaluating retrieval performance...")
        results = self.evaluate_retrieval_performance(dataset, categories)
        
        return results, categories
    
    def analyze_patch_fragmentation(self, dataset, sample_size=50):
        """
        Analyze potential patch fragmentation issues by examining attention patterns
        """
        print("Analyzing patch fragmentation patterns...")
        
        fragmentation_scores = []
        
        with torch.no_grad():
            for i in range(min(sample_size, len(dataset))):
                item = dataset[i]
                
                # Process document image
                inputs = self.processor.process_images(
                    images=[item['document_image']], 
                    return_tensors="pt"
                )
                inputs = {k: v.to(self.device) for k, v in inputs.items()}
                
                # Get attention weights from the model
                outputs = self.model(**inputs, output_attentions=True)
                
                # Analyze attention patterns across patches
                # This is a simplified analysis - you might want more sophisticated metrics
                embeddings = outputs.embeddings[0]  # Shape: (seq_len, emb_dim)
                
                # Compute patch coherence score (simplified)
                patch_similarities = torch.matmul(embeddings, embeddings.T)
                coherence_score = patch_similarities.mean().item()
                
                fragmentation_scores.append({
                    'document_idx': i,
                    'coherence_score': coherence_score,
                    'query': item.get('query', '')
                })
        
        return fragmentation_scores

# Usage Example
def main():
    from transformers import AutoModel, AutoProcessor
    
    # Load ColPali model and processor
    model_name = "vidore/colpali-v1.2"  # or your specific model
    model = AutoModel.from_pretrained(model_name, trust_remote_code=True)
    processor = AutoProcessor.from_pretrained(model_name, trust_remote_code=True)
    
    # Initialize prober
    prober = ColPaliVisualProber(model, processor)
    
    # Run comprehensive evaluation
    results, categories = prober.run_comprehensive_probe()
    
    # Print results
    print("\n=== RETRIEVAL PERFORMANCE RESULTS ===")
    print("\nOverall Performance:")
    for metric, score in results['overall'].items():
        print(f"  {metric}: {score:.4f}")
    
    print("\nPerformance by Category:")
    for category, metrics in results['by_category'].items():
        print(f"\n{category.upper()}:")
        for metric, score in metrics.items():
            print(f"  {metric}: {score:.4f}")
    
    # Analyze fragmentation
    fragmentation_analysis = prober.analyze_patch_fragmentation(
        prober.load_vidore_dataset()
    )
    
    # Save results
    with open('colpali_probe_results.json', 'w') as f:
        json.dump({
            'performance_results': results,
            'categories': {k: len(v) for k, v in categories.items()},
            'fragmentation_analysis': fragmentation_analysis
        }, f, indent=2)
    
    print("\nResults saved to 'colpali_probe_results.json'")

if __name__ == "__main__":
    main()
