{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e24ebf1b25684221b08042964ddca8a5", "version_major": 2, "version_minor": 0}, "text/plain": ["Fetching 2 files:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "76f0bb3e6f9f4d66ab89b2b0fe921031", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Using a slow image processor as `use_fast` is unset and a slow processor was saved with this model. `use_fast=True` will be the default behavior in v4.52, even if the model was saved with a slow processor. This will result in minor differences in outputs. You'll still be able to use a slow processor with `use_fast=False`.\n"]}], "source": ["import torch\n", "from PIL import Image\n", "from transformers.utils.import_utils import is_flash_attn_2_available\n", "\n", "from colpali_engine.models import ColQwen2, ColQwen2Processor\n", "\n", "model_name = \"vidore/colqwen2-v1.0\"\n", "\n", "model = ColQwen2.from_pretrained(\n", "    model_name,\n", "    torch_dtype=torch.bfloat16,\n", "    device_map=\"cuda:0\",  # or \"mps\" if on Apple Silicon\n", "    attn_implementation=\"flash_attention_2\" if is_flash_attn_2_available() else None,\n", ").eval()\n", "\n", "processor = ColQwen2Processor.from_pretrained(model_name)\n", "\n", "# Your inputs\n", "images = [\n", "    Image.new(\"RGB\", (128, 128), color=\"white\"),\n", "    Image.new(\"RGB\", (64, 32), color=\"black\"),\n", "]\n", "queries = [\n", "    \"What is the organizational structure for our R&D department?\",\n", "    \"Can you provide a breakdown of last year’s financial performance?\",\n", "]\n", "\n", "# Process the inputs\n", "batch_images = processor.process_images(images).to(model.device)\n", "batch_queries = processor.process_queries(queries).to(model.device)\n", "\n", "# Forward pass\n", "with torch.no_grad():\n", "    image_embeddings = model(**batch_images)\n", "    query_embeddings = model(**batch_queries)\n", "\n", "scores = processor.score_multi_vector(query_embeddings, image_embeddings)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[7.2188, 7.0312],\n", "        [6.1250, 6.0312]])"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["scores"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<PIL.Image.Image image mode=RGB size=128x128>,\n", " <PIL.Image.Image image mode=RGB size=64x32>]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m<PERSON><PERSON> crashed while executing code in the current cell or a previous cell. \n", "\u001b[1;31m<PERSON><PERSON>se review the code in the cell(s) to identify a possible cause of the failure. \n", "\u001b[1;31mClick <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["images"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "revise", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 2}