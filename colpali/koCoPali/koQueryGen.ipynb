{"cells": [{"cell_type": "code", "execution_count": 10, "metadata": {"metadata": {}}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ac5fdd66fa86405c89e42ad17708b5aa", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/5 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from transformers import Qwen2VLForConditionalGeneration, AutoTokenizer, AutoProcessor\n", "from qwen_vl_utils import process_vision_info\n", "import torch\n", "\n", "model = Qwen2VLForConditionalGeneration.from_pretrained(\n", "    \"Qwen/Qwen2-VL-7B-Instruct\",\n", "    torch_dtype=torch.bfloat16,\n", "    attn_implementation=\"flash_attention_2\",\n", "    device_map=\"auto\",\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"metadata": {}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Using a slow image processor as `use_fast` is unset and a slow processor was saved with this model. `use_fast=True` will be the default behavior in v4.52, even if the model was saved with a slow processor. This will result in minor differences in outputs. You'll still be able to use a slow processor with `use_fast=False`.\n", "You have video processor config saved in `preprocessor.json` file which is deprecated. Video processor configs should be saved in their own `video_preprocessor.json` file. You can rename the file or load and save the processor back which renames it automatically. Loading from `preprocessor.json` will be removed in v5.0.\n"]}], "source": ["# default processer\n", "processor = AutoProcessor.from_pretrained(\"Qwen/Qwen2-VL-7B-Instruct\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"metadata": {}}, "outputs": [], "source": ["messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": [\n", "            {\n", "                \"type\": \"image\",\n", "                \"image\": \"https://qianwen-res.oss-cn-beijing.aliyuncs.com/Qwen-VL/assets/demo.jpeg\",\n", "            },\n", "            {\"type\": \"text\", \"text\": \"Describe this image.\"},\n", "        ],\n", "    }\n", "]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"metadata": {}}, "outputs": [], "source": ["text = processor.apply_chat_template(\n", "    messages, tokenize=False, add_generation_prompt=True\n", ")\n", "image_inputs, video_inputs = process_vision_info(messages)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"metadata": {}}, "outputs": [{"data": {"text/plain": ["'<|im_start|>system\\nYou are a helpful assistant.<|im_end|>\\n<|im_start|>user\\n<|vision_start|><|image_pad|><|vision_end|>Describe this image.<|im_end|>\\n<|im_start|>assistant\\n'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["text"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 15, "metadata": {"metadata": {}}, "outputs": [], "source": ["inputs = processor(\n", "    text=[text],\n", "    images=image_inputs,\n", "    videos=video_inputs,\n", "    padding=True,\n", "    return_tensors=\"pt\",\n", ")\n", "inputs = inputs.to(\"cuda\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"metadata": {}}, "outputs": [{"data": {"text/plain": ["{'input_ids': tensor([[151644,   8948,    198,  ..., 151644,  77091,    198]],\n", "       device='cuda:0'), 'attention_mask': tensor([[1, 1, 1,  ..., 1, 1, 1]], device='cuda:0'), 'pixel_values': tensor([[ 0.8501,  0.8501,  0.8647,  ...,  1.3922,  1.3922,  1.3922],\n", "        [ 0.9376,  0.9376,  0.9376,  ...,  1.4491,  1.4491,  1.4491],\n", "        [ 0.9084,  0.9376,  0.9376,  ...,  1.4065,  1.4207,  1.4207],\n", "        ...,\n", "        [-0.1280, -0.1280, -0.1426,  ..., -0.2431, -0.2715, -0.3000],\n", "        [-0.3324, -0.3324, -0.3032,  ..., -0.3000, -0.2715, -0.2857],\n", "        [-0.3762, -0.4054, -0.4054,  ..., -0.4279, -0.4422, -0.4564]],\n", "       device='cuda:0'), 'image_grid_thw': tensor([[  1,  98, 146]], device='cuda:0')}"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["inputs"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"metadata": {}}, "outputs": [{"data": {"text/plain": ["[\"The image depicts a serene beach scene with a woman and her dog enjoying a moment together. The woman is sitting on the sandy beach, facing the ocean, and appears to be engaging in a playful activity with her dog. The dog, which is a large breed, is sitting on its hind legs and extending its front paw towards the woman's hand. The woman is smiling and seems to be enjoying the interaction, as she is holding out her hand for the dog to touch. \\n\\nThe woman is dressed in casual attire, wearing a plaid shirt and dark pants. She has long hair that is flowing freely in the breeze. The dog is wearing a harness, suggesting that it is well-trained and accustomed to being on a leash. The harness is colorful, with a pattern that includes various shapes and colors.\\n\\nThe beach itself is sandy, with gentle waves lapping at the shore in the background. The sky is clear, and the sun is setting, casting a warm, golden glow over the entire scene\"]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["generated_ids = model.generate(**inputs, max_new_tokens=200)\n", "generated_ids_trimmed = [\n", "    out_ids[len(in_ids) :] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)\n", "]\n", "output_text = processor.batch_decode(\n", "    generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False\n", ")\n", "output_text"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Building Colpali queries"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"metadata": {}}, "outputs": [], "source": ["prompt = \"\"\"\n", "You are an assistant specialized in Multimodal RAG tasks.\n", "\n", "The task is the following: given an image from a pdf page, you will have to generate questions that can be asked by a user to retrieve information from a large documentary corpus.\n", "\n", "The question should be relevant to the page, and should not be too specific or too general. The question should be about the subject of the page, and the answer needs to be found in the page.\n", "\n", "Remember that the question is asked by a user to get some information from a large documentary corpus that contains multimodal data. Generate a question that could be asked by a user without knowing the existence and the content of the corpus.\n", "\n", "Generate as well the answer to the question, which should be found in the page. And the format of the answer should be a list of words answering the question.\n", "\n", "Generate at most THREE pairs of questions and answers per page as JSON with the following format, answer ONLY using JSON, NOTHING ELSE:\n", "\n", "{\n", "    \"questions\": [\n", "        {\n", "            \"question\": \"XXXXXX\",\n", "            \"answer\": [\"YYYYYY\"]\n", "        },\n", "        {\n", "            \"question\": \"XXXXXX\",\n", "            \"answer\": [\"YYYYYY\"]\n", "        },\n", "        {\n", "            \"question\": \"XXXXXX\",\n", "            \"answer\": [\"YYYYYY\"]\n", "        }\n", "    ]\n", "}\n", "\n", "where XXXXXX is the question and ['YYYYYY'] is the corresponding list of answers that could be as long as needed.\n", "\n", "Note: If there are no questions to ask about the page, return an empty list. Focus on making relevant questions concerning the page.\n", "\n", "Here is the page:\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 29, "metadata": {"metadata": {}}, "outputs": [], "source": ["import os\n", "from datasets import load_dataset\n", "\n", "os.environ[\"HF_HUB_ENABLE_HF_TRANSFER\"] = \"1\"\n", "\n", "ds = load_dataset(\"davanstrien/ufo-ColPali\", split=\"train\")"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"metadata": {}}, "outputs": [], "source": ["messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": [\n", "            {\n", "                \"type\": \"image\",\n", "                \"image\": ds[0][\"image\"],\n", "            },\n", "            {\"type\": \"text\", \"text\": prompt},\n", "        ],\n", "    }\n", "]"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"metadata": {}}, "outputs": [{"data": {"text/plain": ["[{'role': 'user',\n", "  'content': [{'type': 'image',\n", "    'image': <PIL.JpegImagePlugin.JpegImageFile image mode=RGB size=596x842>},\n", "   {'type': 'text',\n", "    'text': '\\nYou are an assistant specialized in Multimodal RAG tasks.\\n\\nThe task is the following: given an image from a pdf page, you will have to generate questions that can be asked by a user to retrieve information from a large documentary corpus.\\n\\nThe question should be relevant to the page, and should not be too specific or too general. The question should be about the subject of the page, and the answer needs to be found in the page.\\n\\nRemember that the question is asked by a user to get some information from a large documentary corpus that contains multimodal data. Generate a question that could be asked by a user without knowing the existence and the content of the corpus.\\n\\nGenerate as well the answer to the question, which should be found in the page. And the format of the answer should be a list of words answering the question.\\n\\nGenerate at most THREE pairs of questions and answers per page as JSON with the following format, answer ONLY using JSON, NOTHING ELSE:\\n\\n{\\n    \"questions\": [\\n        {\\n            \"question\": \"XXXXXX\",\\n            \"answer\": [\"YYYYYY\"]\\n        },\\n        {\\n            \"question\": \"XXXXXX\",\\n            \"answer\": [\"YYYYYY\"]\\n        },\\n        {\\n            \"question\": \"XXXXXX\",\\n            \"answer\": [\"YYYYYY\"]\\n        }\\n    ]\\n}\\n\\nwhere XXXXXX is the question and [\\'YYYYYY\\'] is the corresponding list of answers that could be as long as needed.\\n\\nNote: If there are no questions to ask about the page, return an empty list. Focus on making relevant questions concerning the page.\\n\\nHere is the page:\\n'}]}]"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["messages"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"metadata": {}}, "outputs": [], "source": ["text = processor.apply_chat_template(\n", "    messages, tokenize=False, add_generation_prompt=True\n", ")\n", "image_inputs, video_inputs = process_vision_info(messages)\n", "inputs = processor(\n", "    text=[text],\n", "    images=image_inputs,\n", "    videos=video_inputs,\n", "    padding=True,\n", "    return_tensors=\"pt\",\n", ")\n", "inputs = inputs.to(\"cuda\")"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"metadata": {}}, "outputs": [{"data": {"text/plain": ["['{\\n    \"questions\": [\\n        {\\n            \"question\": \"What is the main topic of the document?\",\\n            \"answer\": [\"Blue Book\", \"astronomers\", \"lunar phenomena\"]\\n        },\\n        {\\n            \"question\": \"What is the purpose of the document?\",\\n            \"answer\": [\"top secret\", \"investigation\", \"astronomers\"]\\n        },\\n        {\\n            \"question\": \"What is the author of the document?\",\\n            \"answer\": [\"<PERSON>\"]\\n        }\\n    ]\\n}']"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["generated_ids = model.generate(**inputs, max_new_tokens=200)\n", "generated_ids_trimmed = [\n", "    out_ids[len(in_ids) :] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)\n", "]\n", "output_text = processor.batch_decode(\n", "    generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False\n", ")\n", "output_text"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Validating the Responses"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"metadata": {}}, "outputs": [], "source": ["import json"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"metadata": {}}, "outputs": [{"data": {"text/plain": ["{'questions': [{'question': 'What is the main topic of the document?',\n", "   'answer': ['Blue Book', 'astronomers', 'lunar phenomena']},\n", "  {'question': 'What is the purpose of the document?',\n", "   'answer': ['top secret', 'investigation', 'astronomers']},\n", "  {'question': 'What is the author of the document?',\n", "   'answer': ['<PERSON>']}]}"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["json.loads(output_text[0])"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"metadata": {}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"questions\": [\n", "        {\n", "            \"question\": \"What is the main topic of the document?\",\n", "            \"answer\": [\"Blue Book\", \"astronomers\", \"lunar phenomena\"]\n", "        },\n", "        {\n", "            \"question\": \"What is the purpose of the document?\",\n", "            \"answer\": [\"top secret\", \"investigation\", \"astronomers\"]\n", "        },\n", "        {\n", "            \"question\": \"What is the author of the document?\",\n", "            \"answer\": [\"<PERSON>\"]\n", "        }\n", "    ]\n", "}\n"]}], "source": ["print(output_text[0])"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"metadata": {}}, "outputs": [], "source": ["prompt = \"\"\"You are an AI assistant specialized in document retrieval tasks. Given an image of a document page, your task is to generate retrieval queries that someone might use to find this document in a large corpus.\n", "\n", "Please generate 3 different types of retrieval queries:\n", "\n", "1. A broad topical query: This should cover the main subject of the document.\n", "2. A specific detail query: This should focus on a particular fact, figure, or point made in the document.\n", "3. A visual element query: This should reference a chart, graph, image, or other visual component in the document, if present.\n", "\n", "Important guidelines:\n", "- Ensure the queries are relevant for retrieval tasks, not just describing the page content.\n", "- Frame the queries as if someone is searching for this document, not asking questions about its content.\n", "- Make the queries diverse and representative of different search strategies.\n", "\n", "For each query, also provide a brief explanation of why this query would be effective in retrieving this document.\n", "\n", "Format your response as a JSON object with the following structure:\n", "\n", "{\n", "  \"broad_topical_query\": \"Your query here\",\n", "  \"broad_topical_explanation\": \"Brief explanation\",\n", "  \"specific_detail_query\": \"Your query here\",\n", "  \"specific_detail_explanation\": \"Brief explanation\",\n", "  \"visual_element_query\": \"Your query here\",\n", "  \"visual_element_explanation\": \"Brief explanation\"\n", "}\n", "\n", "If there are no relevant visual elements, replace the third query with another specific detail query.\n", "\n", "Here is the document image to analyze:\n", "<image>\n", "\n", "Generate the queries based on this image and provide the response in the specified JSON format.\"\"\""]}, {"cell_type": "code", "execution_count": 38, "metadata": {"metadata": {}}, "outputs": [], "source": ["from pydantic import BaseModel\n", "from typing import Tuple\n", "\n", "\n", "class GeneralRetrievalQuery(BaseModel):\n", "    broad_topical_query: str\n", "    broad_topical_explanation: str\n", "    specific_detail_query: str\n", "    specific_detail_explanation: str\n", "    visual_element_query: str\n", "    visual_element_explanation: str"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### An Update Retrieval Focused Prompt"]}, {"cell_type": "code", "execution_count": 39, "metadata": {"metadata": {}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prompt for 'general':\n", "You are an AI assistant specialized in document retrieval tasks. Given an image of a document page, your task is to generate retrieval queries that someone might use to find this document in a large corpus.\n", "\n", "Please generate 3 different types of retrieval queries:\n", "\n", "1. A broad topical query: This should cover the main subject of the document.\n", "2. A specific detail query: This should focus on a particular fact, figure, or point made in the document.\n", "3. A visual element query: This should reference a chart, graph, image, or other visual component in the document, if present.\n", "\n", "Important guidelines:\n", "- Ensure the queries are relevant for retrieval tasks, not just describing the page content.\n", "- Frame the queries as if someone is searching for this document, not asking questions about its content.\n", "- Make the queries diverse and representative of different search strategies.\n", "\n", "For each query, also provide a brief explanation of why this query would be effective in retrieving this document.\n", "\n", "Format your response as a JSON object with the following structure:\n", "\n", "{\n", "  \"broad_topical_query\": \"Your query here\",\n", "  \"broad_topical_explanation\": \"Brief explanation\",\n", "  \"specific_detail_query\": \"Your query here\",\n", "  \"specific_detail_explanation\": \"Brief explanation\",\n", "  \"visual_element_query\": \"Your query here\",\n", "  \"visual_element_explanation\": \"Brief explanation\"\n", "}\n", "\n", "If there are no relevant visual elements, replace the third query with another specific detail query.\n", "\n", "Here is the document image to analyze:\n", "<image>\n", "\n", "Generate the queries based on this image and provide the response in the specified JSON format.\n", "\n", "Pydantic model for this prompt: <class '__main__.GeneralRetrievalQuery'>\n"]}], "source": ["def get_retrieval_prompt(prompt_name: str) -> <PERSON><PERSON>[str, General<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]:\n", "    if prompt_name != \"general\":\n", "        raise ValueError(\"Only 'general' prompt is available in this version\")\n", "\n", "    prompt = \"\"\"You are an AI assistant specialized in document retrieval tasks. Given an image of a document page, your task is to generate retrieval queries that someone might use to find this document in a large corpus.\n", "\n", "Please generate 3 different types of retrieval queries:\n", "\n", "1. A broad topical query: This should cover the main subject of the document.\n", "2. A specific detail query: This should focus on a particular fact, figure, or point made in the document.\n", "3. A visual element query: This should reference a chart, graph, image, or other visual component in the document, if present.\n", "\n", "Important guidelines:\n", "- Ensure the queries are relevant for retrieval tasks, not just describing the page content.\n", "- Frame the queries as if someone is searching for this document, not asking questions about its content.\n", "- Make the queries diverse and representative of different search strategies.\n", "\n", "For each query, also provide a brief explanation of why this query would be effective in retrieving this document.\n", "\n", "Format your response as a JSON object with the following structure:\n", "\n", "{\n", "  \"broad_topical_query\": \"Your query here\",\n", "  \"broad_topical_explanation\": \"Brief explanation\",\n", "  \"specific_detail_query\": \"Your query here\",\n", "  \"specific_detail_explanation\": \"Brief explanation\",\n", "  \"visual_element_query\": \"Your query here\",\n", "  \"visual_element_explanation\": \"Brief explanation\"\n", "}\n", "\n", "If there are no relevant visual elements, replace the third query with another specific detail query.\n", "\n", "Here is the document image to analyze:\n", "<image>\n", "\n", "Generate the queries based on this image and provide the response in the specified JSON format.\"\"\"\n", "\n", "    return prompt, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "\n", "\n", "# Example usage:\n", "prompt_name = \"general\"\n", "prompt, pydantic_model = get_retrieval_prompt(prompt_name)\n", "print(f\"Prompt for '{prompt_name}':\")\n", "print(prompt)\n", "print(f\"\\nPydantic model for this prompt: {pydantic_model}\")"]}, {"cell_type": "code", "execution_count": 40, "metadata": {"metadata": {}}, "outputs": [], "source": ["def generate_response(prompt, image):\n", "    messages = [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": [\n", "                {\n", "                    \"type\": \"image\",\n", "                    \"image\": image,\n", "                },\n", "                {\"type\": \"text\", \"text\": prompt},\n", "            ],\n", "        }\n", "    ]\n", "\n", "    text = processor.apply_chat_template(\n", "        messages, tokenize=False, add_generation_prompt=True\n", "    )\n", "\n", "    image_inputs, video_inputs = process_vision_info(messages)\n", "\n", "    inputs = processor(\n", "        text=[text],\n", "        images=image_inputs,\n", "        videos=video_inputs,\n", "        padding=True,\n", "        return_tensors=\"pt\",\n", "    )\n", "    inputs = inputs.to(\"cuda\")\n", "\n", "    generated_ids = model.generate(**inputs, max_new_tokens=200)\n", "    generated_ids_trimmed = [\n", "        out_ids[len(in_ids) :]\n", "        for in_ids, out_ids in zip(inputs.input_ids, generated_ids)\n", "    ]\n", "\n", "    output_text = processor.batch_decode(\n", "        generated_ids_trimmed,\n", "        skip_special_tokens=True,\n", "        clean_up_tokenization_spaces=False,\n", "    )\n", "\n", "    return output_text"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"metadata": {}}, "outputs": [{"data": {"text/plain": ["['{\\n  \"broad_topical_query\": \"Document discussing radioactivity and celestial phenomena\",\\n  \"broad_topical_explanation\": \"This query covers the main subject of the document, which is the study of radioactivity and its effects on celestial phenomena.\",\\n  \"specific_detail_query\": \"Observations of radioactivity during solar eclipses\",\\n  \"specific_detail_explanation\": \"This query focuses on a specific fact mentioned in the document, which is the observation of radioactivity during solar eclipses.\",\\n  \"visual_element_query\": \"Document page with text in French\",\\n  \"visual_element_explanation\": \"This query refers to the visual element of the document page, which contains text in French, indicating that the document is likely in French and contains text about radioactivity and celestial phenomena.\"\\n}']"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["generate_response(prompt, ds[2][\"image\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generating the Full Dataset"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"metadata": {}}, "outputs": [{"data": {"text/plain": ["Dataset({\n", "    features: ['image', 'raw_queries', 'broad_topical_query', 'broad_topical_explanation', 'specific_detail_query', 'specific_detail_explanation', 'visual_element_query', 'visual_element_explanation', 'parsed_into_json'],\n", "    num_rows: 2243\n", "})"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["ds"]}, {"cell_type": "code", "execution_count": 43, "metadata": {"metadata": {}}, "outputs": [], "source": ["sample = ds.take(100)"]}, {"cell_type": "code", "execution_count": 44, "metadata": {"metadata": {}}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "16120b9e50a44a21b8f170bc94aa8ccf", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from tqdm.auto import tqdm\n", "\n", "responses = []\n", "for row in tqdm(sample):\n", "    try:\n", "        resp = generate_response(prompt, row[\"image\"])\n", "        responses.append(resp)\n", "    except Exception as e:\n", "        responses.append(None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["responses[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len([r for r in responses if r is None])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["responses[0][0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["json.loads(responses[0][0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = sample.add_column(\"raw_queries\", responses)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["keys = list(json.loads(responses[0][0]).keys())\n", "keys"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["{k: None for k in keys}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_data(row):\n", "    try:\n", "        data = json.loads(row[\"raw_queries\"][0])\n", "        data[\"parsed_into_json\"] = True\n", "        return data\n", "    except Exception:\n", "        data = {k: None for k in keys}\n", "        data[\"parsed_into_json\"] = False\n", "        return data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["parsed_ds = sample.map(extract_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "\n", "Counter(parsed_ds[\"parsed_into_json\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Pushing to the Hub"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from huggingface_hub import login"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["login()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["parsed_ds.push_to_hub(\"davanstrien/ufo-ColPali\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Appendix: more diverse queries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel\n", "from typing import List, Optional, Tuple, Union\n", "\n", "# Pydantic models for each prompt type\n", "\n", "\n", "class GeneralRetrievalQuery(BaseModel):\n", "    broad_topical_query: str\n", "    broad_topical_explanation: str\n", "    specific_detail_query: str\n", "    specific_detail_explanation: str\n", "    visual_element_query: str\n", "    visual_element_explanation: str\n", "\n", "\n", "class MultiDocumentComparisonQuery(BaseModel):\n", "    comparison_query: str\n", "    comparison_explanation: str\n", "    corroboration_contradiction_query: str\n", "    corroboration_contradiction_explanation: str\n", "\n", "\n", "class DomainSpecificQuery(BaseModel):\n", "    identified_domain: str\n", "    domain_specific_query: str\n", "    domain_specific_explanation: str\n", "    data_findings_query: str\n", "    data_findings_explanation: str\n", "    applications_implications_query: str\n", "    applications_implications_explanation: str\n", "\n", "\n", "class VisualElementFocusQuery(BaseModel):\n", "    similar_visual_element_query: str\n", "    similar_visual_element_explanation: str\n", "    text_visual_combination_query: str\n", "    text_visual_combination_explanation: str\n", "    visual_content_understanding_query: str\n", "    visual_content_understanding_explanation: str\n", "\n", "\n", "class TemporalMetadataQuery(BaseModel):\n", "    temporal_query: str\n", "    temporal_explanation: str\n", "    topic_metadata_combination_query: str\n", "    topic_metadata_combination_explanation: str\n", "    update_related_document_query: str\n", "    update_related_document_explanation: str\n", "\n", "\n", "class DifficultyAmbiguityQuery(BaseModel):\n", "    simple_query: str\n", "    simple_explanation: str\n", "    complex_query: str\n", "    complex_explanation: str\n", "    ambiguous_query: str\n", "    ambiguous_explanation: str\n", "\n", "\n", "class MultilingualMultimodalQuery(BaseModel):\n", "    multilingual_query: str\n", "    multilingual_explanation: str\n", "    multimodal_combination_query: str\n", "    multimodal_combination_explanation: str\n", "    text_visual_understanding_query: str\n", "    text_visual_understanding_explanation: str\n", "\n", "\n", "def get_retrieval_prompt(\n", "    prompt_name: str,\n", ") -> <PERSON><PERSON>[\n", "    str,\n", "    Union[\n", "        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "        MultiDocumentComparisonQuery,\n", "        DomainSpecificQuery,\n", "        VisualElementFocus<PERSON><PERSON><PERSON>,\n", "        TemporalMetadataQuery,\n", "        DifficultyAmbiguityQuery,\n", "        MultilingualMultimo<PERSON><PERSON><PERSON><PERSON>,\n", "    ],\n", "]:\n", "    prompts = {\n", "        \"general\": (\n", "            \"\"\"You are an AI assistant specialized in document retrieval tasks. Given an image of a document page, your task is to generate retrieval queries that someone might use to find this document in a large corpus.\n", "\n", "Please generate 3 different types of retrieval queries:\n", "\n", "1. A broad topical query: This should cover the main subject of the document.\n", "2. A specific detail query: This should focus on a particular fact, figure, or point made in the document.\n", "3. A visual element query: This should reference a chart, graph, image, or other visual component in the document, if present.\n", "\n", "Important guidelines:\n", "- Ensure the queries are relevant for retrieval tasks, not just describing the page content.\n", "- Frame the queries as if someone is searching for this document, not asking questions about its content.\n", "- Make the queries diverse and representative of different search strategies.\n", "\n", "For each query, also provide a brief explanation of why this query would be effective in retrieving this document.\n", "\n", "Format your response as a JSON object with the following structure:\n", "\n", "{\n", "  \"broad_topical_query\": \"Your query here\",\n", "  \"broad_topical_explanation\": \"Brief explanation\",\n", "  \"specific_detail_query\": \"Your query here\",\n", "  \"specific_detail_explanation\": \"Brief explanation\",\n", "  \"visual_element_query\": \"Your query here\",\n", "  \"visual_element_explanation\": \"Brief explanation\"\n", "}\n", "\n", "If there are no relevant visual elements, replace the third query with another specific detail query.\n", "\n", "Here is the document image to analyze:\n", "<image>\n", "\n", "Generate the queries based on this image and provide the response in the specified JSON format.\"\"\",\n", "            <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "        ),\n", "        \"comparison\": (\n", "            \"\"\"Imagine this document page is part of a larger corpus. Your task is to generate retrieval queries that would require comparing this document with others in the corpus.\n", "\n", "Please generate 2 retrieval queries:\n", "\n", "1. A query comparing this document's topic with a related subject\n", "2. A query seeking documents that contradict or support the main point of this page\n", "\n", "For each query, provide a brief explanation of how it encourages document comparison and why it would be effective for retrieval.\n", "\n", "Format your response as a JSON object with the following structure:\n", "\n", "{\n", "  \"comparison_query\": \"Your query here\",\n", "  \"comparison_explanation\": \"Brief explanation\",\n", "  \"corroboration_contradiction_query\": \"Your query here\",\n", "  \"corroboration_contradiction_explanation\": \"Brief explanation\"\n", "}\n", "\n", "Here is the document image to analyze:\n", "<image>\n", "\n", "Generate the queries based on this image and provide the response in the specified JSON format.\"\"\",\n", "            MultiDocumentComparisonQuery,\n", "        ),\n", "        \"domain\": (\n", "            \"\"\"Your task is to create retrieval queries that a professional in the document's domain might use to find this document in a large corpus.\n", "\n", "First, identify the domain of the document (e.g., scientific, financial, legal, medical, technical).\n", "\n", "Then, generate 3 retrieval queries:\n", "\n", "1. A query using domain-specific terminology\n", "2. A query seeking specific data or findings presented in the document\n", "3. A query related to the document's potential applications or implications\n", "\n", "For each query, provide a brief explanation of its relevance to the domain and why it would be effective for retrieval.\n", "\n", "Format your response as a JSON object with the following structure:\n", "\n", "{\n", "  \"identified_domain\": \"Domain name\",\n", "  \"domain_specific_query\": \"Your query here\",\n", "  \"domain_specific_explanation\": \"Brief explanation\",\n", "  \"data_findings_query\": \"Your query here\",\n", "  \"data_findings_explanation\": \"Brief explanation\",\n", "  \"applications_implications_query\": \"Your query here\",\n", "  \"applications_implications_explanation\": \"Brief explanation\"\n", "}\n", "\n", "Here is the document image to analyze:\n", "<image>\n", "\n", "Generate the queries based on this image and provide the response in the specified JSON format.\"\"\",\n", "            DomainSpecificQuery,\n", "        ),\n", "        \"visual\": (\n", "            \"\"\"Your task is to generate retrieval queries focusing on the visual elements in this document page (charts, tables, images, diagrams).\n", "\n", "Please generate 3 retrieval queries:\n", "\n", "1. A query specifically asking for documents with similar visual elements\n", "2. A query combining textual and visual information\n", "3. A query that would require understanding the content of the visual element to retrieve this document\n", "\n", "For each query, provide a brief explanation of how it incorporates visual elements and why it would be effective for retrieval.\n", "\n", "Format your response as a JSON object with the following structure:\n", "\n", "{\n", "  \"similar_visual_element_query\": \"Your query here\",\n", "  \"similar_visual_element_explanation\": \"Brief explanation\",\n", "  \"text_visual_combination_query\": \"Your query here\",\n", "  \"text_visual_combination_explanation\": \"Brief explanation\",\n", "  \"visual_content_understanding_query\": \"Your query here\",\n", "  \"visual_content_understanding_explanation\": \"Brief explanation\"\n", "}\n", "\n", "If the document lacks significant visual elements, explain this and generate alternative queries focusing on the document's structure or layout.\n", "\n", "Here is the document image to analyze:\n", "<image>\n", "\n", "Generate the queries based on this image and provide the response in the specified JSON format.\"\"\",\n", "            VisualElementFocus<PERSON><PERSON><PERSON>,\n", "        ),\n", "        \"temporal\": (\n", "            \"\"\"Assuming this document is part of a large, diverse corpus, your task is to generate retrieval queries that incorporate metadata or temporal aspects.\n", "\n", "Please generate 3 retrieval queries:\n", "\n", "1. A query specifying a likely time frame for this document\n", "2. A query combining topical information with a metadata element (e.g., author, publication type)\n", "3. A query seeking updated or related documents on the same topic\n", "\n", "For each query, provide a brief explanation of how it uses temporal or metadata information and why it would be effective for retrieval.\n", "\n", "Format your response as a JSON object with the following structure:\n", "\n", "{\n", "  \"temporal_query\": \"Your query here\",\n", "  \"temporal_explanation\": \"Brief explanation\",\n", "  \"topic_metadata_combination_query\": \"Your query here\",\n", "  \"topic_metadata_combination_explanation\": \"Brief explanation\",\n", "  \"update_related_document_query\": \"Your query here\",\n", "  \"update_related_document_explanation\": \"Brief explanation\"\n", "}\n", "\n", "Here is the document image to analyze:\n", "<image>\n", "\n", "Generate the queries based on this image and provide the response in the specified JSON format.\"\"\",\n", "            TemporalMetadataQuery,\n", "        ),\n", "        \"difficulty\": (\n", "            \"\"\"Your task is to create retrieval queries for this document at different levels of complexity and ambiguity.\n", "\n", "Please generate 3 retrieval queries:\n", "\n", "1. A simple, straightforward query\n", "2. A complex query requiring understanding of multiple aspects of the document\n", "3. An ambiguous query that could retrieve this document among others\n", "\n", "For each query, provide a brief explanation of its complexity level or ambiguity and why it would be effective or challenging for retrieval.\n", "\n", "Format your response as a JSON object with the following structure:\n", "\n", "{\n", "  \"simple_query\": \"Your query here\",\n", "  \"simple_explanation\": \"Brief explanation\",\n", "  \"complex_query\": \"Your query here\",\n", "  \"complex_explanation\": \"Brief explanation\",\n", "  \"ambiguous_query\": \"Your query here\",\n", "  \"ambiguous_explanation\": \"Brief explanation\"\n", "}\n", "\n", "Here is the document image to analyze:\n", "<image>\n", "\n", "Generate the queries based on this image and provide the response in the specified JSON format.\"\"\",\n", "            DifficultyAmbiguityQuery,\n", "        ),\n", "        \"multilingual\": (\n", "            \"\"\"Your task is to generate retrieval queries considering potential multilingual and multi-modal aspects of the document.\n", "\n", "Please generate 3 retrieval queries:\n", "\n", "1. A query in a different language (if applicable) that would retrieve this document\n", "2. A query combining textual and non-textual elements\n", "3. A query that requires understanding both the text and visual elements to retrieve this document accurately\n", "\n", "For each query, provide a brief explanation of its multilingual or multi-modal nature and why it would be effective for retrieval.\n", "\n", "Format your response as a JSON object with the following structure:\n", "\n", "{\n", "  \"multilingual_query\": \"Your query here\",\n", "  \"multilingual_explanation\": \"Brief explanation\",\n", "  \"multimodal_combination_query\": \"Your query here\",\n", "  \"multimodal_combination_explanation\": \"Brief explanation\",\n", "  \"text_visual_understanding_query\": \"Your query here\",\n", "  \"text_visual_understanding_explanation\": \"Brief explanation\"\n", "}\n", "\n", "If the document is not suitable for multilingual queries, explain why and provide an alternative query.\n", "\n", "Here is the document image to analyze:\n", "<image>\n", "\n", "Generate the queries based on this image and provide the response in the specified JSON format.\"\"\",\n", "            MultilingualMultimo<PERSON><PERSON><PERSON><PERSON>,\n", "        ),\n", "    }\n", "\n", "    if prompt_name not in prompts:\n", "        raise ValueError(\n", "            f\"Invalid prompt name. Choose from: {', '.join(prompts.keys())}\"\n", "        )\n", "\n", "    return prompts[prompt_name]\n", "\n", "\n", "# Example usage:\n", "prompt_name = \"general\"  # You can change this to any of the available prompt names\n", "prompt, pydantic_model = get_retrieval_prompt(prompt_name)\n", "print(f\"Prompt for '{prompt_name}':\")\n", "print(prompt)\n", "print(f\"\\nPydantic model for this prompt: {pydantic_model}\")"]}], "metadata": {"kernelspec": {"display_name": "revise", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 2}