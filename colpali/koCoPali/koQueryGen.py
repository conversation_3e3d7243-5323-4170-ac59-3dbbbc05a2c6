# -*- coding: utf-8 -*-
import sys
import locale

# Set UTF-8 encoding for the entire script
if sys.stdout.encoding != 'utf-8':
    sys.stdout.reconfigure(encoding='utf-8')
if sys.stderr.encoding != 'utf-8':
    sys.stderr.reconfigure(encoding='utf-8')

# Set locale to handle Korean text properly
try:
    locale.setlocale(locale.LC_ALL, 'ko_KR.UTF-8')
except locale.Error:
    try:
        locale.setlocale(locale.LC_ALL, 'C.UTF-8')
    except locale.Error:
        pass  # Use default locale

from transformers import Qwen2VLForConditionalGeneration, AutoTokenizer, AutoProcessor
from qwen_vl_utils import process_vision_info
import torch
from tqdm.auto import tqdm
import json
from pydantic import BaseModel
from typing import Tuple
import os
from datasets import Dataset
from PIL import Image

# Set CUDA_VISIBLE_DEVICES to limit to GPUs 0-3
os.environ["CUDA_VISIBLE_DEVICES"] = "0,1,2,3"
device_map='auto'


model = Qwen2VLForConditionalGeneration.from_pretrained(
    "Qwen/Qwen2-VL-7B-Instruct",
    torch_dtype=torch.bfloat16,
    attn_implementation="flash_attention_2",
    device_map=device_map,
)

# default processer
processor = AutoProcessor.from_pretrained("Qwen/Qwen2-VL-7B-Instruct")

# Load images from local directory
def load_local_images_to_dataset(dir_path):
    """Load images from local directory into a HuggingFace Dataset format"""
    if not os.path.exists(dir_path):
        raise FileNotFoundError(f"Directory {dir_path} not found")
    
    # Get all image files
    image_files = [f for f in os.listdir(dir_path) 
                   if f.lower().endswith(('png', 'jpg', 'jpeg', 'bmp', 'gif'))]
    
    # Load images as PIL objects
    images = []
    for i, img_file in enumerate(image_files):
        img_path = os.path.join(dir_path, img_file)
        with Image.open(img_path) as img:
            images.append(img.copy())  # Copy to keep image after file closes
        if i == 2:
            break

    # Create dataset dictionary
    return Dataset.from_dict({"image": images})

# Replace the original dataset loading with local images
os.environ["HF_HUB_ENABLE_HF_TRANSFER"] = "1"

# Load local images instead of remote dataset
print("Image Loading...\n")
ds = load_local_images_to_dataset("./data/image/")
print(f"Loaded {len(ds)} images from local directory")

class GeneralRetrievalQuery(BaseModel):
    broad_topical_query: str
    broad_topical_explanation: str
    specific_detail_query: str
    specific_detail_explanation: str
    visual_element_query: str
    visual_element_explanation: str

def get_retrieval_prompt(prompt_name: str) -> Tuple[str, GeneralRetrievalQuery]:
    if prompt_name != "general":
        raise ValueError("Only 'general' prompt is available in this version")

    prompt = """당신은 문서 검색 작업을 전문으로 하는 AI 어시스턴트입니다. 문서 페이지의 이미지가 주어졌을 때, 대규모 문서 코퍼스에서 이 문서를 찾기 위해 사용할 수 있는 검색 쿼리를 생성하는 것이 당신의 임무입니다.

다음 3가지 유형의 검색 쿼리를 **한국어로** 생성해주세요:

1. 광범위한 주제 쿼리: 문서의 주요 주제를 다루어야 합니다.
2. 구체적인 세부 사항 쿼리: 문서에서 언급된 특정 사실, 수치, 또는 요점에 초점을 맞춰야 합니다.
3. 시각적 요소 쿼리: 문서에 있는 차트, 그래프, 이미지, 또는 기타 시각적 구성 요소를 참조해야 합니다.

중요한 가이드라인:
- 모든 쿼리를 한국어로 생성하세요
- 쿼리가 페이지 내용을 단순히 설명하는 것이 아니라 검색 작업에 관련성이 있도록 하세요
- 내용에 대한 질문이 아니라 이 문서를 검색하는 사람의 관점에서 쿼리를 구성하세요
- 쿼리가 다양하고 서로 다른 검색 전략을 대표하도록 하세요

각 쿼리에 대해 이 쿼리가 해당 문서를 검색하는 데 효과적인 이유에 대한 간단한 설명도 제공하세요.

다음 구조의 JSON 객체 형식으로 응답하세요:

{
"broad_topical_query": "한국어 쿼리",
"broad_topical_explanation": "간단한 설명",
"specific_detail_query": "한국어 쿼리",
"specific_detail_explanation": "간단한 설명",
"visual_element_query": "한국어 쿼리",
"visual_element_explanation": "간단한 설명"
}

관련 시각적 요소가 없는 경우, 세 번째 쿼리를 다른 구체적인 세부 사항 쿼리로 대체하세요.

분석할 문서 이미지는 다음과 같습니다:
<image>

이 이미지를 바탕으로 모든 쿼리를 한국어로 생성하여 지정된 JSON 형식으로 응답해주세요."""

    return prompt, GeneralRetrievalQuery


# Example usage:
prompt_name = "general"
prompt, pydantic_model = get_retrieval_prompt(prompt_name)
print(f"Prompt for '{prompt_name}':")
print(prompt)
print(f"\nPydantic model for this prompt: {pydantic_model}")

def generate_response(prompt, image):
    messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "image",
                    "image": image,
                },
                {"type": "text", "text": prompt},
            ],
        }
    ]

    text = processor.apply_chat_template(
        messages, tokenize=False, add_generation_prompt=True
    )

    image_inputs, video_inputs = process_vision_info(messages)

    inputs = processor(
        text=[text],
        images=image_inputs,
        videos=video_inputs,
        padding=True,
        return_tensors="pt",
    )
    inputs = inputs.to("cuda")

    generated_ids = model.generate(**inputs, max_new_tokens=1024)
    generated_ids_trimmed = [
        out_ids[len(in_ids) :]
        for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
    ]

    output_text = processor.batch_decode(
        generated_ids_trimmed,
        skip_special_tokens=True,
        clean_up_tokenization_spaces=False,
    )

    return output_text

sample = ds.take(1)

responses = []
for row in tqdm(sample):
    try:
        resp = generate_response(prompt, row["image"])
        responses.append(resp)
    except Exception as e:
        responses.append(None)


sample = sample.add_column("raw_queries", responses)

# Get keys from the first successful response
keys = []
for response in responses:
    if response is not None:
        try:
            keys = list(json.loads(response[0]).keys())
            break
        except (json.JSONDecodeError, IndexError, TypeError):
            continue

# If no successful parsing, use default keys
if not keys:
    keys = ["broad_topical_query", "broad_topical_explanation",
            "specific_detail_query", "specific_detail_explanation",
            "visual_element_query", "visual_element_explanation"]

def extract_data(row):
    try:
        if row["raw_queries"] is None:
            data = {k: None for k in keys}
            data["parsed_into_json"] = False
            return data

        response_text = row["raw_queries"][0]
        # Clean up the response text to handle potential encoding issues
        if isinstance(response_text, bytes):
            response_text = response_text.decode('utf-8', errors='ignore')

        data = json.loads(response_text)
        data["parsed_into_json"] = True
        return data
    except (json.JSONDecodeError, IndexError, TypeError, UnicodeDecodeError) as e:
        print(f"Error parsing response: {e}")
        data = {k: None for k in keys}
        data["parsed_into_json"] = False
        data["error"] = str(e)
        return data

parsed_ds = sample.map(extract_data)

# Save with proper UTF-8 encoding to handle Korean text
import pandas as pd

# Convert to pandas DataFrame first for better control over encoding
df = parsed_ds.to_pandas()

# Save as JSON with UTF-8 encoding
df.to_json("ko_ColPali.json", orient="records", lines=True, force_ascii=False)

# Alternative: Save with explicit UTF-8 encoding using standard json module
with open("ko_ColPali_backup.json", "w", encoding="utf-8") as f:
    for record in df.to_dict("records"):
        json.dump(record, f, ensure_ascii=False)
        f.write("\n")

# from collections import Counter

# Counter(parsed_ds["parsed_into_json"])

# # %%
# from huggingface_hub import login

# # %%
# login()

# # %%
# parsed_ds.push_to_hub("davanstrien/ufo-ColPali")
