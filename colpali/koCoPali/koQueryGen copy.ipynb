from transformers import Qwen2VLForConditionalGeneration, AutoTokenizer, AutoProcessor
from qwen_vl_utils import process_vision_info
import torch

model = Qwen2VLForConditionalGeneration.from_pretrained(
    "Qwen/Qwen2-VL-7B-Instruct",
    torch_dtype=torch.bfloat16,
    attn_implementation="flash_attention_2",
    device_map="auto",
)

# default processer
processor = AutoProcessor.from_pretrained("Qwen/Qwen2-VL-7B-Instruct")

import os
from datasets import Dataset
from PIL import Image

# Load images from local directory
def load_local_images_to_dataset(dir_path):
    """Load images from local directory into a HuggingFace Dataset format"""
    if not os.path.exists(dir_path):
        raise FileNotFoundError(f"Directory {dir_path} not found")
    
    # Get all image files
    image_files = [f for f in os.listdir(dir_path) 
                   if f.lower().endswith(('png', 'jpg', 'jpeg', 'bmp', 'gif'))]
    
    # Load images as PIL objects
    images = []
    for i, img_file in enumerate(image_files):
        img_path = os.path.join(dir_path, img_file)
        with Image.open(img_path) as img:
            images.append(img.copy())  # Copy to keep image after file closes
        if i == 10:
            break

    # Create dataset dictionary
    return Dataset.from_dict({"image": images})

# Replace the original dataset loading with local images
os.environ["HF_HUB_ENABLE_HF_TRANSFER"] = "1"

# Load local images instead of remote dataset
ds = load_local_images_to_dataset("./data/image/")
print(f"Loaded {len(ds)} images from local directory")


import json

from pydantic import BaseModel
from typing import Tuple


class GeneralRetrievalQuery(BaseModel):
    broad_topical_query: str
    broad_topical_explanation: str
    specific_detail_query: str
    specific_detail_explanation: str
    visual_element_query: str
    visual_element_explanation: str

def get_retrieval_prompt(prompt_name: str) -> Tuple[str, GeneralRetrievalQuery]:
    if prompt_name != "general":
        raise ValueError("Only 'general' prompt is available in this version")

    prompt = """당신은 문서 검색 작업을 전문으로 하는 AI 어시스턴트입니다. 문서 페이지의 이미지가 주어졌을 때, 대규모 문서 코퍼스에서 이 문서를 찾기 위해 사용할 수 있는 검색 쿼리를 생성하는 것이 당신의 임무입니다.

    다음 3가지 유형의 검색 쿼리를 **한국어로** 생성해주세요:

    1. 광범위한 주제 쿼리: 문서의 주요 주제를 다루어야 합니다.
    2. 구체적인 세부 사항 쿼리: 문서에서 언급된 특정 사실, 수치, 또는 요점에 초점을 맞춰야 합니다.
    3. 시각적 요소 쿼리: 문서에 있는 차트, 그래프, 이미지, 또는 기타 시각적 구성 요소를 참조해야 합니다.

    중요한 가이드라인:
    - 모든 쿼리를 한국어로 생성하세요
    - 쿼리가 페이지 내용을 단순히 설명하는 것이 아니라 검색 작업에 관련성이 있도록 하세요
    - 내용에 대한 질문이 아니라 이 문서를 검색하는 사람의 관점에서 쿼리를 구성하세요
    - 쿼리가 다양하고 서로 다른 검색 전략을 대표하도록 하세요

    각 쿼리에 대해 이 쿼리가 해당 문서를 검색하는 데 효과적인 이유에 대한 간단한 설명도 제공하세요.

    다음 구조의 JSON 객체 형식으로 응답하세요:

    {
    "broad_topical_query": "한국어 쿼리",
    "broad_topical_explanation": "간단한 설명",
    "specific_detail_query": "한국어 쿼리",
    "specific_detail_explanation": "간단한 설명",
    "visual_element_query": "한국어 쿼리",
    "visual_element_explanation": "간단한 설명"
    }

    관련 시각적 요소가 없는 경우, 세 번째 쿼리를 다른 구체적인 세부 사항 쿼리로 대체하세요.

    분석할 문서 이미지는 다음과 같습니다:
    <image>

    이 이미지를 바탕으로 모든 쿼리를 한국어로 생성하여 지정된 JSON 형식으로 응답해주세요."""


    return prompt, GeneralRetrievalQuery


# Example usage:
prompt_name = "general"
prompt, pydantic_model = get_retrieval_prompt(prompt_name)
print(f"Prompt for '{prompt_name}':")
print(prompt)
print(f"\nPydantic model for this prompt: {pydantic_model}")

def generate_response(prompt, image):
    messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "image",
                    "image": image,
                },
                {"type": "text", "text": prompt},
            ],
        }
    ]

    text = processor.apply_chat_template(
        messages, tokenize=False, add_generation_prompt=True
    )

    image_inputs, video_inputs = process_vision_info(messages)

    inputs = processor(
        text=[text],
        images=image_inputs,
        videos=video_inputs,
        padding=True,
        return_tensors="pt",
    )
    inputs = inputs.to("cuda")

    generated_ids = model.generate(**inputs, max_new_tokens=1024)
    generated_ids_trimmed = [
        out_ids[len(in_ids) :]
        for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
    ]

    output_text = processor.batch_decode(
        generated_ids_trimmed,
        skip_special_tokens=True,
        clean_up_tokenization_spaces=False,
    )

    return output_text

ds

sample = ds.take(10)

from tqdm.auto import tqdm

responses = []
for row in tqdm(sample):
    try:
        resp = generate_response(prompt, row["image"])
        responses.append(resp)
    except Exception as e:
        responses.append(None)

responses[0]

len([r for r in responses if r is None])

responses[0][0]

json.loads(responses[0][0])

sample = sample.add_column("raw_queries", responses)

sample

keys = list(json.loads(responses[0][0]).keys())
keys

{k: None for k in keys}

def extract_data(row):
    try:
        data = json.loads(row["raw_queries"][0])
        data["parsed_into_json"] = True
        return data
    except Exception:
        data = {k: None for k in keys}
        data["parsed_into_json"] = False
        return data

parsed_ds = sample.map(extract_data)

from collections import Counter

Counter(parsed_ds["parsed_into_json"])

from huggingface_hub import login

login()

parsed_ds.push_to_hub("johnandru/ko-ColPali")

parsed_ds[0]['image']

from pydantic import BaseModel
from typing import List, Optional, Tuple, Union

# Pydantic models for each prompt type


class GeneralRetrievalQuery(BaseModel):
    broad_topical_query: str
    broad_topical_explanation: str
    specific_detail_query: str
    specific_detail_explanation: str
    visual_element_query: str
    visual_element_explanation: str


class MultiDocumentComparisonQuery(BaseModel):
    comparison_query: str
    comparison_explanation: str
    corroboration_contradiction_query: str
    corroboration_contradiction_explanation: str


class DomainSpecificQuery(BaseModel):
    identified_domain: str
    domain_specific_query: str
    domain_specific_explanation: str
    data_findings_query: str
    data_findings_explanation: str
    applications_implications_query: str
    applications_implications_explanation: str


class VisualElementFocusQuery(BaseModel):
    similar_visual_element_query: str
    similar_visual_element_explanation: str
    text_visual_combination_query: str
    text_visual_combination_explanation: str
    visual_content_understanding_query: str
    visual_content_understanding_explanation: str


class TemporalMetadataQuery(BaseModel):
    temporal_query: str
    temporal_explanation: str
    topic_metadata_combination_query: str
    topic_metadata_combination_explanation: str
    update_related_document_query: str
    update_related_document_explanation: str


class DifficultyAmbiguityQuery(BaseModel):
    simple_query: str
    simple_explanation: str
    complex_query: str
    complex_explanation: str
    ambiguous_query: str
    ambiguous_explanation: str


class MultilingualMultimodalQuery(BaseModel):
    multilingual_query: str
    multilingual_explanation: str
    multimodal_combination_query: str
    multimodal_combination_explanation: str
    text_visual_understanding_query: str
    text_visual_understanding_explanation: str


def get_retrieval_prompt(
    prompt_name: str,
) -> Tuple[
    str,
    Union[
        GeneralRetrievalQuery,
        MultiDocumentComparisonQuery,
        DomainSpecificQuery,
        VisualElementFocusQuery,
        TemporalMetadataQuery,
        DifficultyAmbiguityQuery,
        MultilingualMultimodalQuery,
    ],
]:
    prompts = {
        "general": (
            """You are an AI assistant specialized in document retrieval tasks. Given an image of a document page, your task is to generate retrieval queries that someone might use to find this document in a large corpus.

Please generate 3 different types of retrieval queries:

1. A broad topical query: This should cover the main subject of the document.
2. A specific detail query: This should focus on a particular fact, figure, or point made in the document.
3. A visual element query: This should reference a chart, graph, image, or other visual component in the document, if present.

Important guidelines:
- Ensure the queries are relevant for retrieval tasks, not just describing the page content.
- Frame the queries as if someone is searching for this document, not asking questions about its content.
- Make the queries diverse and representative of different search strategies.

For each query, also provide a brief explanation of why this query would be effective in retrieving this document.

Format your response as a JSON object with the following structure:

{
  "broad_topical_query": "Your query here",
  "broad_topical_explanation": "Brief explanation",
  "specific_detail_query": "Your query here",
  "specific_detail_explanation": "Brief explanation",
  "visual_element_query": "Your query here",
  "visual_element_explanation": "Brief explanation"
}

If there are no relevant visual elements, replace the third query with another specific detail query.

Here is the document image to analyze:
<image>

Generate the queries based on this image and provide the response in the specified JSON format.""",
            GeneralRetrievalQuery,
        ),
        "comparison": (
            """Imagine this document page is part of a larger corpus. Your task is to generate retrieval queries that would require comparing this document with others in the corpus.

Please generate 2 retrieval queries:

1. A query comparing this document's topic with a related subject
2. A query seeking documents that contradict or support the main point of this page

For each query, provide a brief explanation of how it encourages document comparison and why it would be effective for retrieval.

Format your response as a JSON object with the following structure:

{
  "comparison_query": "Your query here",
  "comparison_explanation": "Brief explanation",
  "corroboration_contradiction_query": "Your query here",
  "corroboration_contradiction_explanation": "Brief explanation"
}

Here is the document image to analyze:
<image>

Generate the queries based on this image and provide the response in the specified JSON format.""",
            MultiDocumentComparisonQuery,
        ),
        "domain": (
            """Your task is to create retrieval queries that a professional in the document's domain might use to find this document in a large corpus.

First, identify the domain of the document (e.g., scientific, financial, legal, medical, technical).

Then, generate 3 retrieval queries:

1. A query using domain-specific terminology
2. A query seeking specific data or findings presented in the document
3. A query related to the document's potential applications or implications

For each query, provide a brief explanation of its relevance to the domain and why it would be effective for retrieval.

Format your response as a JSON object with the following structure:

{
  "identified_domain": "Domain name",
  "domain_specific_query": "Your query here",
  "domain_specific_explanation": "Brief explanation",
  "data_findings_query": "Your query here",
  "data_findings_explanation": "Brief explanation",
  "applications_implications_query": "Your query here",
  "applications_implications_explanation": "Brief explanation"
}

Here is the document image to analyze:
<image>

Generate the queries based on this image and provide the response in the specified JSON format.""",
            DomainSpecificQuery,
        ),
        "visual": (
            """Your task is to generate retrieval queries focusing on the visual elements in this document page (charts, tables, images, diagrams).

Please generate 3 retrieval queries:

1. A query specifically asking for documents with similar visual elements
2. A query combining textual and visual information
3. A query that would require understanding the content of the visual element to retrieve this document

For each query, provide a brief explanation of how it incorporates visual elements and why it would be effective for retrieval.

Format your response as a JSON object with the following structure:

{
  "similar_visual_element_query": "Your query here",
  "similar_visual_element_explanation": "Brief explanation",
  "text_visual_combination_query": "Your query here",
  "text_visual_combination_explanation": "Brief explanation",
  "visual_content_understanding_query": "Your query here",
  "visual_content_understanding_explanation": "Brief explanation"
}

If the document lacks significant visual elements, explain this and generate alternative queries focusing on the document's structure or layout.

Here is the document image to analyze:
<image>

Generate the queries based on this image and provide the response in the specified JSON format.""",
            VisualElementFocusQuery,
        ),
        "temporal": (
            """Assuming this document is part of a large, diverse corpus, your task is to generate retrieval queries that incorporate metadata or temporal aspects.

Please generate 3 retrieval queries:

1. A query specifying a likely time frame for this document
2. A query combining topical information with a metadata element (e.g., author, publication type)
3. A query seeking updated or related documents on the same topic

For each query, provide a brief explanation of how it uses temporal or metadata information and why it would be effective for retrieval.

Format your response as a JSON object with the following structure:

{
  "temporal_query": "Your query here",
  "temporal_explanation": "Brief explanation",
  "topic_metadata_combination_query": "Your query here",
  "topic_metadata_combination_explanation": "Brief explanation",
  "update_related_document_query": "Your query here",
  "update_related_document_explanation": "Brief explanation"
}

Here is the document image to analyze:
<image>

Generate the queries based on this image and provide the response in the specified JSON format.""",
            TemporalMetadataQuery,
        ),
        "difficulty": (
            """Your task is to create retrieval queries for this document at different levels of complexity and ambiguity.

Please generate 3 retrieval queries:

1. A simple, straightforward query
2. A complex query requiring understanding of multiple aspects of the document
3. An ambiguous query that could retrieve this document among others

For each query, provide a brief explanation of its complexity level or ambiguity and why it would be effective or challenging for retrieval.

Format your response as a JSON object with the following structure:

{
  "simple_query": "Your query here",
  "simple_explanation": "Brief explanation",
  "complex_query": "Your query here",
  "complex_explanation": "Brief explanation",
  "ambiguous_query": "Your query here",
  "ambiguous_explanation": "Brief explanation"
}

Here is the document image to analyze:
<image>

Generate the queries based on this image and provide the response in the specified JSON format.""",
            DifficultyAmbiguityQuery,
        ),
        "multilingual": (
            """Your task is to generate retrieval queries considering potential multilingual and multi-modal aspects of the document.

Please generate 3 retrieval queries:

1. A query in a different language (if applicable) that would retrieve this document
2. A query combining textual and non-textual elements
3. A query that requires understanding both the text and visual elements to retrieve this document accurately

For each query, provide a brief explanation of its multilingual or multi-modal nature and why it would be effective for retrieval.

Format your response as a JSON object with the following structure:

{
  "multilingual_query": "Your query here",
  "multilingual_explanation": "Brief explanation",
  "multimodal_combination_query": "Your query here",
  "multimodal_combination_explanation": "Brief explanation",
  "text_visual_understanding_query": "Your query here",
  "text_visual_understanding_explanation": "Brief explanation"
}

If the document is not suitable for multilingual queries, explain why and provide an alternative query.

Here is the document image to analyze:
<image>

Generate the queries based on this image and provide the response in the specified JSON format.""",
            MultilingualMultimodalQuery,
        ),
    }

    if prompt_name not in prompts:
        raise ValueError(
            f"Invalid prompt name. Choose from: {', '.join(prompts.keys())}"
        )

    return prompts[prompt_name]


# Example usage:
prompt_name = "general"  # You can change this to any of the available prompt names
prompt, pydantic_model = get_retrieval_prompt(prompt_name)
print(f"Prompt for '{prompt_name}':")
print(prompt)
print(f"\nPydantic model for this prompt: {pydantic_model}")